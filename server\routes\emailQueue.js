const express = require('express');
const router = express.Router();
const EmailQueueService = require('../services/emailQueueService');
const { cleanupQueueAndScheduleFollowups } = require('../scripts/queue-cleanup-and-followup');

const queueService = new EmailQueueService();

// GET /api/queue/status - Get queue status and statistics
router.get('/status', async (req, res) => {
  try {
    const status = await queueService.getQueueStatus();
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('❌ Error getting queue status:', error.message);
    res.status(500).json({ 
      error: 'Failed to get queue status',
      details: error.message
    });
  }
});

// POST /api/queue/add - Add contact to queue
router.post('/add', async (req, res) => {
  try {
    const { contactId, emailType, tone, priority } = req.body;
    
    if (!contactId) {
      return res.status(400).json({ error: 'Contact ID is required' });
    }

    const queueEntry = await queueService.addToQueue(contactId, {
      emailType,
      tone,
      priority
    });

    res.json({
      success: true,
      data: queueEntry
    });
  } catch (error) {
    console.error('❌ Error adding to queue:', error.message);
    res.status(500).json({ 
      error: 'Failed to add to queue',
      details: error.message
    });
  }
});

// DELETE /api/queue/remove/:queueId - Remove from queue
router.delete('/remove/:queueId', async (req, res) => {
  try {
    const { queueId } = req.params;
    
    await queueService.removeFromQueue(queueId);

    res.json({
      success: true,
      message: 'Removed from queue'
    });
  } catch (error) {
    console.error('❌ Error removing from queue:', error.message);
    res.status(500).json({ 
      error: 'Failed to remove from queue',
      details: error.message
    });
  }
});

// GET /api/queue/settings - Get queue settings
router.get('/settings', async (req, res) => {
  try {
    const settings = await queueService.getSettings();
    res.json({
      success: true,
      data: settings.toJSON()
    });
  } catch (error) {
    console.error('❌ Error getting settings:', error.message);
    res.status(500).json({ 
      error: 'Failed to get settings',
      details: error.message
    });
  }
});

// PUT /api/queue/settings - Update queue settings
router.put('/settings', async (req, res) => {
  try {
    const updatedSettings = await queueService.updateSettings(req.body);
    res.json({
      success: true,
      data: updatedSettings
    });
  } catch (error) {
    console.error('❌ Error updating settings:', error.message);
    res.status(500).json({
      error: 'Failed to update settings',
      details: error.message
    });
  }
});

// POST /api/queue/cleanup-and-followup - Clean up failed contacts and schedule follow-ups
router.post('/cleanup-and-followup', async (req, res) => {
  try {
    console.log('🧹 Starting queue cleanup and follow-up scheduling via API...');

    await cleanupQueueAndScheduleFollowups();

    res.json({
      success: true,
      message: 'Queue cleanup and follow-up scheduling completed successfully'
    });
  } catch (error) {
    console.error('❌ Error during cleanup and follow-up scheduling:', error.message);
    res.status(500).json({
      error: 'Failed to cleanup queue and schedule follow-ups',
      details: error.message
    });
  }
});

// POST /api/queue/start - Start queue processor
router.post('/start', async (req, res) => {
  try {
    queueService.startProcessor();
    res.json({
      success: true,
      message: 'Queue processor started'
    });
  } catch (error) {
    console.error('❌ Error starting processor:', error.message);
    res.status(500).json({ 
      error: 'Failed to start processor',
      details: error.message
    });
  }
});

// POST /api/queue/stop - Stop queue processor
router.post('/stop', async (req, res) => {
  try {
    queueService.stopProcessor();
    res.json({
      success: true,
      message: 'Queue processor stopped'
    });
  } catch (error) {
    console.error('❌ Error stopping processor:', error.message);
    res.status(500).json({ 
      error: 'Failed to stop processor',
      details: error.message
    });
  }
});

// POST /api/queue/process - Manually trigger queue processing
router.post('/process', async (req, res) => {
  try {
    await queueService.processQueue();
    const status = await queueService.getQueueStatus();
    res.json({
      success: true,
      message: 'Queue processed',
      data: status
    });
  } catch (error) {
    console.error('❌ Error processing queue:', error.message);
    res.status(500).json({
      error: 'Failed to process queue',
      details: error.message
    });
  }
});

// POST /api/queue/schedule-now - Reschedule all queued emails for immediate sending
router.post('/schedule-now', async (req, res) => {
  try {
    const result = await queueService.scheduleAllForNow();
    res.json({
      success: true,
      message: 'Emails rescheduled for immediate sending',
      updatedCount: result.updatedCount
    });
  } catch (error) {
    console.error('❌ Error rescheduling emails:', error.message);
    res.status(500).json({
      error: 'Failed to reschedule emails',
      details: error.message
    });
  }
});

// PUT /api/queue/update-schedule/:queueId - Update scheduled time for a specific queue item
router.put('/update-schedule/:queueId', async (req, res) => {
  try {
    const { queueId } = req.params;
    const { scheduledFor } = req.body;

    if (!scheduledFor) {
      return res.status(400).json({ error: 'scheduledFor is required' });
    }

    const result = await queueService.updateSchedule(queueId, scheduledFor);
    res.json({
      success: true,
      message: 'Schedule updated successfully',
      data: result
    });
  } catch (error) {
    console.error('❌ Error updating schedule:', error.message);
    res.status(500).json({
      error: 'Failed to update schedule',
      details: error.message
    });
  }
});

module.exports = router;
