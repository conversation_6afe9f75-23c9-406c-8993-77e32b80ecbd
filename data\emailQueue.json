[{"id": "907b7bda-3300-4253-9615-b9b7841bff70", "contactId": "946f7307-9d65-47a2-a749-84ed3490deff", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "The Novel <PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-20T20:18:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-20T20:18:13.674Z", "createdAt": "2025-08-20T20:04:00.027Z", "updatedAt": "2025-08-20T20:18:13.675Z"}, {"id": "0ee769d1-42ce-4d66-87e2-0c789a6557ae", "contactId": "8d243ee7-3f93-4fac-a50a-b7cba26782e2", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Char & Stave", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-20T21:30:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-20T22:18:36.471Z", "createdAt": "2025-08-20T20:04:05.059Z", "updatedAt": "2025-08-20T22:18:36.472Z"}, {"id": "d83b3d78-0217-4d66-821e-0695ab31b78a", "contactId": "675cf342-a462-48ad-a658-98f82ef66cf9", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "CraveWell Juicery & Cafe", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-20T23:16:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-20T23:16:35.247Z", "createdAt": "2025-08-20T20:04:07.115Z", "updatedAt": "2025-08-20T23:16:35.248Z"}, {"id": "d293d286-94b3-4638-808f-078c3d4c3fda", "contactId": "dfa50acc-692b-4be5-acff-60745e35a8e8", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Cake Life Bake Shop", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-21T00:42:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-21T01:38:04.645Z", "createdAt": "2025-08-20T20:04:17.083Z", "updatedAt": "2025-08-21T01:38:04.648Z"}, {"id": "0e609316-c5e1-4d0e-bf8a-845048a6ac34", "contactId": "f432bada-e377-4667-bde3-b784429841cb", "contactName": "Aveley Farms Coffee Roasters", "contactEmail": "<EMAIL>", "contactCompany": "Aveley Farms Coffee Roasters", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-21T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-21T14:13:14.187Z", "createdAt": "2025-08-20T20:10:44.229Z", "updatedAt": "2025-08-21T14:13:14.188Z"}, {"id": "49045459-d814-445e-815f-cf1db18be1f2", "contactId": "36e78e5e-7bd9-4a0a-86e3-84d6e2445a77", "contactName": "Blue Brew Coffee Shop", "contactEmail": "<EMAIL>", "contactCompany": "Blue Brew Coffee Shop", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-21T15:47:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-21T16:25:18.026Z", "createdAt": "2025-08-20T20:10:50.518Z", "updatedAt": "2025-08-21T16:25:18.027Z"}, {"id": "55336de7-e9e8-4a4a-93fa-b185124f91db", "contactId": "c0bd0c29-3b29-44fd-adb8-c16cb3bd0c06", "contactName": "Green Line Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Green Line Cafe", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-21T19:05:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-21T19:41:48.779Z", "createdAt": "2025-08-20T20:10:54.760Z", "updatedAt": "2025-08-21T19:41:48.791Z"}, {"id": "ea293c56-1814-4361-9021-1e9d821fd8bd", "contactId": "ac37c2b9-0eab-4bc6-8d57-77a0c4735e9b", "contactName": "Devon Lawrence", "contactEmail": "<EMAIL>", "contactCompany": "Old Trail Tavern", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:00:28.569Z", "createdAt": "2025-08-20T20:11:14.601Z", "updatedAt": "2025-08-23T13:00:28.570Z"}, {"id": "228a2a89-24f6-408e-b2aa-0c45dcd43750", "contactId": "e512c5b3-1227-47ab-ae9c-0a3cfb32ef30", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "DellaVino Imports, LLC", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T14:30:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:01:28.849Z", "createdAt": "2025-08-20T20:11:17.590Z", "updatedAt": "2025-08-23T13:01:28.850Z"}, {"id": "08c45045-0d72-434c-b55a-51614f226720", "contactId": "2820f0b6-bb0a-4198-a8dd-fbc9181c34ef", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Stove & Co Restaurant Group", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T17:41:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:02:29.415Z", "createdAt": "2025-08-20T20:11:23.530Z", "updatedAt": "2025-08-23T13:02:29.416Z"}, {"id": "0c79ffa8-80c8-4ef8-b7e4-c81e2d8b7c33", "contactId": "1eeb28a0-f9f2-4737-9d04-1fbd4e6502a4", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "The Flintridge Proper Neighborhood Restaurant & Bar", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T18:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:03:29.175Z", "createdAt": "2025-08-20T20:11:28.044Z", "updatedAt": "2025-08-23T13:03:29.180Z"}, {"id": "011819d9-f798-4d55-aa0c-f4e6ae6910d6", "contactId": "097f5433-5264-4373-89d5-3c93a86ebd93", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Baron <PERSON> LLC", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T20:10:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:04:26.847Z", "createdAt": "2025-08-20T20:11:32.116Z", "updatedAt": "2025-08-23T13:04:26.851Z"}, {"id": "7693c1dc-37de-4ddc-a635-d0289ada2c19", "contactId": "878b68bf-657d-43db-bc98-cba1eddb342f", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "m2o Burgers & Salads", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-25T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-25T13:42:00.551Z", "createdAt": "2025-08-20T20:11:38.755Z", "updatedAt": "2025-08-25T13:42:00.572Z"}, {"id": "263f3789-1f9c-4767-8afe-b825f2fb72d9", "contactId": "52aa1f53-235e-4d90-a971-a9d5d7b3ac98", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-25T15:27:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-25T15:27:26.839Z", "createdAt": "2025-08-20T20:11:58.260Z", "updatedAt": "2025-08-25T15:27:26.840Z"}, {"id": "58766974-acd1-47bd-a4e8-a4b5c1e67326", "contactId": "bb1df581-fcd2-4b8f-aa51-c50b3649b00e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-25T18:34:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-25T18:35:06.355Z", "createdAt": "2025-08-20T20:12:02.014Z", "updatedAt": "2025-08-25T18:35:06.357Z"}, {"id": "0be1e1c3-d66b-4b7c-a50d-3971b680fcfb", "contactId": "463e0fcf-67c5-486f-891e-ac7f5232a2b2", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-26T16:12:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-26T16:41:13.283Z", "createdAt": "2025-08-20T20:12:11.964Z", "updatedAt": "2025-08-26T16:41:13.295Z"}, {"id": "fa191683-b5e1-49bb-a7d5-aed8dd82d819", "contactId": "60b98b92-953b-40fe-a568-64cbd435bcf7", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-26T17:33:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-26T19:59:12.553Z", "createdAt": "2025-08-20T20:12:14.534Z", "updatedAt": "2025-08-26T19:59:12.555Z"}, {"id": "379e4046-9552-4154-91d9-533b949af3e0", "contactId": "00cc4457-8472-4d93-a93d-3c7f099198c5", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-26T20:06:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-27T00:10:04.317Z", "createdAt": "2025-08-20T20:12:17.929Z", "updatedAt": "2025-08-27T00:10:04.367Z"}, {"id": "10d660fc-654a-46de-980b-4eb72e1b2e85", "contactId": "c9ee3ac3-2c44-44c3-a953-5dab3f1ab972", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Freshido", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-27T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-27T14:14:19.167Z", "createdAt": "2025-08-20T20:12:32.885Z", "updatedAt": "2025-08-27T14:14:19.171Z"}, {"id": "bb1e73b5-6a3c-48aa-b33d-c70529225bcc", "contactId": "7c6c05a3-abe9-4e3c-baa9-560b8cab0410", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fleetwood Foods LLC", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-27T15:55:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-27T15:55:47.022Z", "createdAt": "2025-08-20T20:12:42.348Z", "updatedAt": "2025-08-27T15:55:47.025Z"}, {"id": "ef6bcebd-c888-4030-b510-5fd8c848752a", "contactId": "d947fd3d-d937-4e61-8649-294c83407eca", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Hakan <PERSON>han Restaurants", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-27T18:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-27T18:59:11.658Z", "createdAt": "2025-08-20T20:12:44.244Z", "updatedAt": "2025-08-27T18:59:11.660Z"}, {"id": "7cd7407e-f9e0-4c64-84aa-476f2c1ffbdd", "contactId": "ea8149b0-d971-431c-bad7-02d0cb8dc768", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Conshy Girls Restaurant Group", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-27T23:59:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T01:02:41.482Z", "createdAt": "2025-08-23T11:51:34.899Z", "updatedAt": "2025-08-28T01:02:41.483Z"}, {"id": "1be35b96-db03-485d-833c-d9ac99c4c2cf", "contactId": "0f7db24c-b606-4aff-809a-c8048ca33fad", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "AWSM Sauce Co", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T01:22:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T01:22:44.054Z", "createdAt": "2025-08-23T11:51:41.959Z", "updatedAt": "2025-08-28T01:22:44.056Z"}, {"id": "f9d280b5-598a-43ee-868b-17837c68c90e", "contactId": "11b50445-477a-46be-b0bb-5cc975b56a93", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T16:01:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T18:11:36.010Z", "createdAt": "2025-08-23T11:52:23.795Z", "updatedAt": "2025-08-28T18:11:36.012Z"}, {"id": "cce0657c-c21a-4bb1-8e77-6a5ebf88f055", "contactId": "384daffa-097d-439d-9526-a481fd0a9d1b", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T18:36:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T18:36:30.621Z", "createdAt": "2025-08-23T11:52:31.620Z", "updatedAt": "2025-08-28T18:36:30.622Z"}, {"id": "7f665403-9142-4923-913d-db4db824a405", "contactId": "2eddba57-e13a-4b85-a579-4a89aae9c0fc", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T21:21:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T21:21:19.862Z", "createdAt": "2025-08-23T11:52:34.983Z", "updatedAt": "2025-08-28T21:21:19.864Z"}, {"id": "a50a7d60-45ce-4ad0-b647-2b371f698d8d", "contactId": "ab7fba09-1f69-4996-a469-854b3e0d0b7e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T23:10:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T23:25:32.017Z", "createdAt": "2025-08-23T11:52:36.993Z", "updatedAt": "2025-08-28T23:25:32.052Z"}, {"id": "71c97eb6-3001-4d62-8cd2-75217de604b9", "contactId": "877ab8a5-6592-449d-ae0a-6f07a57eae7c", "contactName": "The Cafe Luna", "contactEmail": "<EMAIL>", "contactCompany": "The Cafe Luna", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-29T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-29T17:02:43.779Z", "createdAt": "2025-08-23T11:52:57.275Z", "updatedAt": "2025-08-29T17:02:43.781Z"}, {"id": "6098a3b1-bdf3-4cc8-8e26-dcd98e4d43d1", "contactId": "27444ed2-f5fc-4109-93e8-17d7a161060c", "contactName": "Troublemaker Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Troublemaker Cafe", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-29T14:55:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-29T17:03:39.967Z", "createdAt": "2025-08-23T11:52:59.582Z", "updatedAt": "2025-08-29T17:03:39.968Z"}, {"id": "6a245601-0989-477b-9494-c8275c61d1ab", "contactId": "42f89a2d-c35e-4e87-86c6-d9e74031e6a2", "contactName": "Stauf's Coffee", "contactEmail": "<EMAIL>", "contactCompany": "Stauf's Coffee", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-29T16:14:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-29T17:08:47.960Z", "createdAt": "2025-08-23T11:53:02.335Z", "updatedAt": "2025-08-29T17:08:47.962Z"}, {"id": "472204f1-5d21-4e69-91d0-0d9d0f4a3104", "contactId": "9e9a3da1-0d0a-4bdb-ba19-396e50329b75", "contactName": "Rothrock Coffee", "contactEmail": "<EMAIL>", "contactCompany": "Rothrock Coffee", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-29T23:12:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:04:36.348Z", "createdAt": "2025-08-23T11:53:11.822Z", "updatedAt": "2025-09-01T15:04:36.376Z"}, {"id": "5a8155ce-d07b-4217-88f0-0aa19e31dbac", "contactId": "34f69daa-8fb2-4b00-940d-39d16141edc3", "contactName": "Patron Saint Cafe + Bar", "contactEmail": "<EMAIL>", "contactCompany": "Patron Saint Cafe + Bar", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T01:46:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:05:29.342Z", "createdAt": "2025-08-23T11:53:14.315Z", "updatedAt": "2025-09-01T15:05:29.444Z"}, {"id": "fb04656e-42f0-4f1b-aef0-3151830653e5", "contactId": "1696cf18-f31f-4672-8b9e-1c4331a8376d", "contactName": "Monigram Coffee Roasters", "contactEmail": "<EMAIL>", "contactCompany": "Monigram Coffee Roasters", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:06:33.619Z", "createdAt": "2025-08-23T11:53:16.472Z", "updatedAt": "2025-09-01T15:06:33.627Z"}, {"id": "28b243a7-159a-4bc3-81fe-4ad914dfd90a", "contactId": "926af4fd-5f2b-4b20-a830-3308f6deb792", "contactName": "Matter of Taste: Coffee Bar", "contactEmail": "<EMAIL>", "contactCompany": "Matter of Taste: Coffee Bar", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T14:35:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:07:29.433Z", "createdAt": "2025-08-23T11:53:20.921Z", "updatedAt": "2025-09-01T15:07:29.434Z"}, {"id": "8f268ef1-3e8c-4fe7-b90d-d8c2edbfd5db", "contactId": "f4ca6358-0a90-46ec-af8f-9023722ee683", "contactName": "Kitty Brew Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Kitty Brew Cafe", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T16:57:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:08:29.881Z", "createdAt": "2025-08-23T11:53:25.189Z", "updatedAt": "2025-09-01T15:08:29.883Z"}, {"id": "300d5b78-67e2-4b17-9d14-4670ec9398dd", "contactId": "8790d7f5-cb20-4f08-a146-f423dcca17e3", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fox Meadows Creamery, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T19:51:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:09:27.240Z", "createdAt": "2025-08-27T00:54:48.124Z", "updatedAt": "2025-09-01T15:09:27.241Z"}, {"id": "9bf5c0ab-f170-4bba-9d44-101b1424e640", "contactId": "1acafafa-ebfc-4603-bfaf-6487339b845a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fox Meadows Creamery, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T22:34:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:10:25.222Z", "createdAt": "2025-08-27T00:54:51.837Z", "updatedAt": "2025-09-01T15:10:25.225Z"}, {"id": "3fb66482-d768-4178-a455-5a567b9d280f", "contactId": "ab963732-f7ab-4062-9528-5aa160b1f3a0", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Elizabethtown Coffee Company", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-31T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:11:29.220Z", "createdAt": "2025-08-27T00:54:55.684Z", "updatedAt": "2025-09-01T15:11:29.221Z"}, {"id": "7e10587e-43dc-48d6-b807-16c47efda048", "contactId": "ef6d11e4-5cdc-4c8a-a3ae-ebf1703d89d7", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-31T16:59:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T17:13:01.411Z", "createdAt": "2025-08-27T00:55:01.246Z", "updatedAt": "2025-09-01T17:13:01.412Z"}, {"id": "d0c19475-fc36-4b47-a249-5adca9da0e40", "contactId": "b7c38229-4ef0-4c53-a3d6-32c43e59fca4", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-31T21:18:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T17:14:01.319Z", "createdAt": "2025-08-27T00:55:04.881Z", "updatedAt": "2025-09-01T17:14:01.320Z"}, {"id": "162893b4-176b-4073-a5bb-6a7b29a783fc", "contactId": "ce9396ad-e0c9-4a65-806a-cc3a65e32502", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Toasted and Roasted", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T00:21:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:24:21.569Z", "createdAt": "2025-08-27T00:55:07.507Z", "updatedAt": "2025-09-02T17:24:21.587Z"}, {"id": "643c2d90-96f8-4254-bc08-48dbe92f8029", "contactId": "48c94450-2429-4810-90f5-eea0e3531964", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Bewley Irish Imports", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:25:16.931Z", "createdAt": "2025-08-27T00:55:09.907Z", "updatedAt": "2025-09-02T17:25:16.937Z"}, {"id": "55b67259-5c7b-4f5e-a518-bd60c1cedfa7", "contactId": "d477c416-6ad1-4bcb-bafb-d9c192bdc857", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T14:25:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:26:15.114Z", "createdAt": "2025-08-29T20:33:18.066Z", "updatedAt": "2025-09-02T17:26:15.115Z"}, {"id": "3140c4c5-64d0-4f32-9b37-374b61844d5e", "contactId": "f5d8e935-5b14-427b-b705-3afd4c38adf3", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Albertsons Companies", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T17:05:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:27:15.563Z", "createdAt": "2025-08-29T20:33:21.883Z", "updatedAt": "2025-09-02T17:27:15.564Z"}, {"id": "6b42ee20-c15b-4fc2-98a0-80e7aa9a4c94", "contactId": "99252a7a-8ca5-47f5-82d1-826412f1ba1a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Brown's Super Stores, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T19:27:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:28:17.733Z", "createdAt": "2025-08-29T20:33:25.532Z", "updatedAt": "2025-09-02T17:28:17.734Z"}, {"id": "f6d49958-a708-4293-a669-6a21b400b42b", "contactId": "f95cb6e0-ab09-4b10-8681-8c5bf457210a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T22:56:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:29:13.899Z", "createdAt": "2025-08-29T20:33:29.361Z", "updatedAt": "2025-09-02T17:29:13.902Z"}, {"id": "c2f3fb37-7f8f-472e-a0fd-58865972007c", "contactId": "f82ffbca-1eca-49be-9216-e8f92e629280", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Wakefern Food Corp.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T01:35:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:30:15.862Z", "createdAt": "2025-08-29T20:33:32.008Z", "updatedAt": "2025-09-02T17:30:15.863Z"}, {"id": "6dbeaae9-3c96-4ed9-a628-91c483037509", "contactId": "8080e396-9b49-4494-8385-a39c9a59f249", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:31:16.522Z", "createdAt": "2025-08-29T20:33:33.790Z", "updatedAt": "2025-09-02T17:31:16.525Z"}, {"id": "5cdc2a0d-3199-43f3-9030-accd4cf352a1", "contactId": "da1f0ba1-d34a-45f9-ad6e-806ecdfe9ab9", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Dollar General", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T13:26:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:32:15.466Z", "createdAt": "2025-08-29T20:33:36.562Z", "updatedAt": "2025-09-02T17:32:15.468Z"}, {"id": "b3f7c77e-9866-462d-8fb4-4b13c7591ab9", "contactId": "00c5d6aa-c573-4fdc-b07e-ac5967602b8c", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>'s Company", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T16:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:33:16.289Z", "createdAt": "2025-08-29T20:33:38.561Z", "updatedAt": "2025-09-02T17:33:16.290Z"}, {"id": "c900ad9d-74fc-4a2a-b2a9-151cdd991d83", "contactId": "83cf39bf-d8dd-4735-a1f2-adddc5863186", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Wakefern Food Corp.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T21:32:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:01:37.691Z", "createdAt": "2025-08-29T20:33:43.336Z", "updatedAt": "2025-09-03T18:01:37.693Z"}, {"id": "e36d2da8-e049-41f4-9307-1eb31cfba9a3", "contactId": "9f82ea5b-3631-4506-b1d4-2f971bf0ba7f", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T23:45:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:02:38.809Z", "createdAt": "2025-08-29T20:33:45.473Z", "updatedAt": "2025-09-03T18:02:38.813Z"}, {"id": "b0a21081-c8d2-4353-b42a-2dd38a95a26e", "contactId": "864a0796-515d-44b7-ac9e-0129fe162743", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Walmart", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-03T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:03:36.710Z", "createdAt": "2025-08-29T20:33:47.789Z", "updatedAt": "2025-09-03T18:03:36.711Z"}, {"id": "06c877bd-f4cf-4c54-a8d9-48aea4c4b3a3", "contactId": "e236df63-e6ee-4236-b589-e154e4bebd62", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON>s", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-03T13:11:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:04:39.890Z", "createdAt": "2025-08-29T20:33:50.230Z", "updatedAt": "2025-09-03T18:04:39.891Z"}, {"id": "edae6f8e-09b5-46e9-b54a-8f79b48eb0a5", "contactId": "a498cb34-dd27-41b1-adcc-b20308bb2f15", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-03T15:33:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:05:36.641Z", "createdAt": "2025-08-29T20:33:52.459Z", "updatedAt": "2025-09-03T18:05:36.642Z"}, {"id": "3f7a8d8b-df64-484e-abae-1b3cec4e98ca", "contactId": "4b3abd2d-70e6-431f-a8d7-3cd666e55bde", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Albertsons Companies", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-03T17:40:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:06:39.761Z", "createdAt": "2025-08-29T20:33:54.263Z", "updatedAt": "2025-09-03T18:06:39.762Z"}, {"id": "2f6f45e9-0e9f-4bf5-9867-2948881e6441", "contactId": "ffe8a079-0ba7-489b-9ccf-054c9125934e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Whole Foods Market", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-04T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-04T13:26:15.333Z", "createdAt": "2025-08-29T20:34:03.818Z", "updatedAt": "2025-09-04T13:26:15.335Z"}, {"id": "512181d0-3052-4959-9b39-684dd82e8995", "contactId": "addd7d1f-f2ef-4b22-8a91-a809f4d4c5ca", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-04T16:40:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-04T17:37:34.203Z", "createdAt": "2025-08-29T20:34:16.221Z", "updatedAt": "2025-09-04T17:37:34.226Z"}, {"id": "bc89422f-c263-4f30-a270-ce39f8769d86", "contactId": "1bbe6dfd-8f2e-4a30-ad4e-07ac087619ef", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-04T22:18:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:47:28.360Z", "createdAt": "2025-08-29T20:34:24.628Z", "updatedAt": "2025-09-05T19:47:28.398Z"}, {"id": "4abcc895-3bda-4c8b-87f6-169f02785b95", "contactId": "b4371c4c-b720-4d3a-9693-d29bc3b0cc2b", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T01:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:48:24.159Z", "createdAt": "2025-08-29T20:34:26.822Z", "updatedAt": "2025-09-05T19:48:24.174Z"}, {"id": "75e6a6ba-1640-4299-9813-738d57a2815f", "contactId": "90ca84f3-005a-4e81-a25e-cbd79c2e5979", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:49:23.866Z", "createdAt": "2025-08-29T20:34:29.318Z", "updatedAt": "2025-09-05T19:49:23.872Z"}, {"id": "2e83c6cd-786c-4619-bfb0-43b775b9a33d", "contactId": "5a05fe8e-d191-46cd-a738-2b9f219fe252", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T12:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:50:21.809Z", "createdAt": "2025-08-29T20:34:31.857Z", "updatedAt": "2025-09-05T19:50:21.814Z"}, {"id": "3d2e089c-675e-4e49-bb7f-90a33fac219d", "contactId": "3b79c30a-e83c-4bfb-9f14-cf1ef3830f24", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T16:22:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:51:23.855Z", "createdAt": "2025-08-29T20:34:33.789Z", "updatedAt": "2025-09-05T19:51:23.856Z"}, {"id": "e5bd5506-a70b-4048-9b7a-ef1d40d963c4", "contactId": "496235fb-c624-4457-86e1-2981a4079ab1", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Weis Markets", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T18:36:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:52:22.272Z", "createdAt": "2025-08-29T20:34:37.132Z", "updatedAt": "2025-09-05T19:52:22.273Z"}, {"id": "9fed376e-b5f0-4115-9d90-554248d2bfc2", "contactId": "43b6efed-9612-496d-b13e-b510594daa73", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T21:20:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T21:20:24.385Z", "createdAt": "2025-08-29T20:34:41.092Z", "updatedAt": "2025-09-05T21:20:24.386Z"}, {"id": "749073a2-4ce4-4fcc-bd65-aa4965587837", "contactId": "8474035a-613a-4b81-bd0d-b0555934d79f", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Whole Foods Market", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T00:32:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T14:59:20.192Z", "createdAt": "2025-08-29T20:34:42.753Z", "updatedAt": "2025-09-08T14:59:20.216Z"}, {"id": "37da050d-7300-4c91-b39c-f26b16b6d213", "contactId": "206940fd-3406-49f1-81f0-495d181c08d1", "contactName": "<PERSON>", "contactEmail": "michael.ma<PERSON><PERSON>@gianteagle.com", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:00:19.888Z", "createdAt": "2025-08-29T20:34:45.612Z", "updatedAt": "2025-09-08T15:00:19.892Z"}, {"id": "9138c239-82ae-42c3-8c35-d733b0f9e4e4", "contactId": "b90558dd-9055-48a7-ab0a-21314e2f9c37", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Sam's Club", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T13:32:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:01:12.420Z", "createdAt": "2025-08-29T20:34:48.580Z", "updatedAt": "2025-09-08T15:01:12.424Z"}, {"id": "8b1da15e-1cb3-4cad-99bb-62bfa37609c8", "contactId": "0bc7e3c6-4988-40e9-9050-f25eab38a638", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T16:22:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:02:14.474Z", "createdAt": "2025-08-29T20:34:50.745Z", "updatedAt": "2025-09-08T15:02:14.496Z"}, {"id": "d1fd5e30-a99b-476e-b7f4-b45ee020b390", "contactId": "1d79cb72-23c4-4c21-88d3-96684b1eae0b", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T18:50:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:03:16.496Z", "createdAt": "2025-08-29T20:34:56.565Z", "updatedAt": "2025-09-08T15:03:16.497Z"}, {"id": "4b35b01d-dd13-41ce-a698-f159da1277e3", "contactId": "2b413848-67ac-4748-9e4e-9d7e37c2c137", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "OTR", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T22:03:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:04:14.489Z", "createdAt": "2025-08-29T20:34:59.017Z", "updatedAt": "2025-09-08T15:04:14.491Z"}, {"id": "5619c3b1-b8df-4531-9d14-5f78a1047a86", "contactId": "ca8876db-9724-4995-8783-01ea9a9c7787", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T00:54:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:05:16.836Z", "createdAt": "2025-08-29T20:35:00.608Z", "updatedAt": "2025-09-08T15:05:16.837Z"}, {"id": "2ab3b68d-04e4-4c9e-90df-b01dfefdb693", "contactId": "ca668847-f95e-4bfa-86b3-a92f87b78c07", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Leg Up Farmers Market", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:06:15.963Z", "createdAt": "2025-08-29T20:35:03.329Z", "updatedAt": "2025-09-08T15:06:15.964Z"}, {"id": "1291e14e-d56e-4189-b847-1440c9416f62", "contactId": "058d6ea7-9a70-4d4e-8df6-e50323744978", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "BJ's Wholesale Club", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T13:58:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:07:12.884Z", "createdAt": "2025-08-29T20:35:08.097Z", "updatedAt": "2025-09-08T15:07:12.885Z"}, {"id": "5c27065d-afcf-4690-9120-75a2c5790bf9", "contactId": "e0c6233b-385e-418c-94c3-83b454f88d5d", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "RITE AID", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T16:19:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:08:13.601Z", "createdAt": "2025-08-29T20:35:10.507Z", "updatedAt": "2025-09-08T15:08:13.603Z"}, {"id": "8b43c13f-8165-41ea-97e8-73491b5a9fdc", "contactId": "5b44a2af-ce0e-4b51-b052-e709c8073e8d", "contactName": "Victoria Basham", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T18:16:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:09:23.874Z", "createdAt": "2025-08-29T20:35:13.193Z", "updatedAt": "2025-09-08T15:09:23.875Z"}, {"id": "f0f0d35d-8c41-4bbb-b563-0bf70949af47", "contactId": "946f7307-9d65-47a2-a749-84ed3490deff", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "The Novel <PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.619Z", "updatedAt": "2025-09-08T20:14:47.558Z"}, {"id": "6f3ab4dc-6377-4e78-89e0-08ade555b614", "contactId": "8d243ee7-3f93-4fac-a50a-b7cba26782e2", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Char & Stave", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T12:15:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.620Z", "updatedAt": "2025-09-08T20:14:47.576Z"}, {"id": "fa69e916-20d9-48f7-94bf-43d12353cb37", "contactId": "675cf342-a462-48ad-a658-98f82ef66cf9", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "CraveWell Juicery & Cafe", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T14:07:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.620Z", "updatedAt": "2025-09-08T20:14:47.577Z"}, {"id": "8e437a70-879b-405d-bb95-259edfc367df", "contactId": "dfa50acc-692b-4be5-acff-60745e35a8e8", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Cake Life Bake Shop", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T15:40:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.621Z", "updatedAt": "2025-09-08T20:14:47.577Z"}, {"id": "ce0257d4-fc8a-42d0-ae3c-e9b6a55ec481", "contactId": "f432bada-e377-4667-bde3-b784429841cb", "contactName": "Aveley Farms Coffee Roasters", "contactEmail": "<EMAIL>", "contactCompany": "Aveley Farms Coffee Roasters", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T16:58:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.622Z", "updatedAt": "2025-09-08T20:14:47.578Z"}, {"id": "3345f2cf-83fa-42b1-8ff5-7b69f1340e7d", "contactId": "36e78e5e-7bd9-4a0a-86e3-84d6e2445a77", "contactName": "Blue Brew Coffee Shop", "contactEmail": "<EMAIL>", "contactCompany": "Blue Brew Coffee Shop", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T18:40:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.623Z", "updatedAt": "2025-09-08T20:14:47.578Z"}, {"id": "6b236f76-f34a-4a25-8793-6f149ac6b65c", "contactId": "c0bd0c29-3b29-44fd-adb8-c16cb3bd0c06", "contactName": "Green Line Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Green Line Cafe", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T19:51:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.624Z", "updatedAt": "2025-09-08T20:14:47.578Z"}, {"id": "8dc7fad1-00cf-4424-b6a4-decb56173c6a", "contactId": "ac37c2b9-0eab-4bc6-8d57-77a0c4735e9b", "contactName": "Devon Lawrence", "contactEmail": "<EMAIL>", "contactCompany": "Old Trail Tavern", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T21:30:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.624Z", "updatedAt": "2025-09-08T20:14:47.579Z"}, {"id": "69625719-3aed-4add-b8e3-d3c6ff6f9a44", "contactId": "e512c5b3-1227-47ab-ae9c-0a3cfb32ef30", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "DellaVino Imports, LLC", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T22:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.625Z", "updatedAt": "2025-09-08T20:14:47.579Z"}, {"id": "0038882a-2d2d-463b-80fe-a0cc79a03def", "contactId": "2820f0b6-bb0a-4198-a8dd-fbc9181c34ef", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Stove & Co Restaurant Group", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T00:02:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.625Z", "updatedAt": "2025-09-08T20:14:47.579Z"}, {"id": "262bf56d-d398-42fb-949f-3e27b8d0e275", "contactId": "1eeb28a0-f9f2-4737-9d04-1fbd4e6502a4", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "The Flintridge Proper Neighborhood Restaurant & Bar", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.626Z", "updatedAt": "2025-09-08T20:14:47.579Z"}, {"id": "7c51d9c3-c0e6-483e-9d44-e6c1c1e6d26a", "contactId": "097f5433-5264-4373-89d5-3c93a86ebd93", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Baron <PERSON> LLC", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T12:24:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.626Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "990a779b-ae2d-43a0-bda8-2d54e89afd5f", "contactId": "878b68bf-657d-43db-bc98-cba1eddb342f", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "m2o Burgers & Salads", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T13:47:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.627Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "c9dc49dd-ce33-45bf-ae6a-fecad1f4d6bf", "contactId": "52aa1f53-235e-4d90-a971-a9d5d7b3ac98", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T14:51:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.627Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "767f4f85-07f6-4615-92bf-78b321e5fac6", "contactId": "bb1df581-fcd2-4b8f-aa51-c50b3649b00e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T16:46:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.628Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "cd8039cb-4bab-4838-9dd0-132529a3c45e", "contactId": "463e0fcf-67c5-486f-891e-ac7f5232a2b2", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T18:01:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.628Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "939af70b-8072-4960-9a2a-85c4332973d1", "contactId": "60b98b92-953b-40fe-a568-64cbd435bcf7", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T19:16:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.628Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "41200541-727f-4cfa-9cac-7bbc08b69e46", "contactId": "00cc4457-8472-4d93-a93d-3c7f099198c5", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T20:34:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.629Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "983eb82e-757b-45cd-972f-1cb8b423e9c7", "contactId": "c9ee3ac3-2c44-44c3-a953-5dab3f1ab972", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Freshido", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T22:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.629Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "15da1bcf-3ef9-465c-8513-6bbc8b875689", "contactId": "7c6c05a3-abe9-4e3c-baa9-560b8cab0410", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fleetwood Foods LLC", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T23:43:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.630Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "ebea3ba5-8d5f-41a0-b04c-f6f145af7491", "contactId": "d947fd3d-d937-4e61-8649-294c83407eca", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Hakan <PERSON>han Restaurants", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-11T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.630Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "f0341966-8e67-4701-80d7-5b0213197c62", "contactId": "ea8149b0-d971-431c-bad7-02d0cb8dc768", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Conshy Girls Restaurant Group", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-11T12:03:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.630Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "ecd15fdd-71b9-47f1-9ff3-573b3e347322", "contactId": "0f7db24c-b606-4aff-809a-c8048ca33fad", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "AWSM Sauce Co", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-11T13:50:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.631Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "d63dc68e-d8d2-46ea-8d62-10890fd1e499", "contactId": "11b50445-477a-46be-b0bb-5cc975b56a93", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-11T15:09:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.632Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "7c85250a-388c-4c46-b2af-d46a9129b3d3", "contactId": "384daffa-097d-439d-9526-a481fd0a9d1b", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-11T16:39:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.632Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "449b8267-e585-4f4d-b3f4-016411445b38", "contactId": "2eddba57-e13a-4b85-a579-4a89aae9c0fc", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-11T17:42:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.633Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "820f2ec9-7607-4f34-b7db-22770b2063b1", "contactId": "ab7fba09-1f69-4996-a469-854b3e0d0b7e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-11T19:24:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.633Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "f439729c-537a-4fa7-adc1-8e0f0ce93dd6", "contactId": "877ab8a5-6592-449d-ae0a-6f07a57eae7c", "contactName": "The Cafe Luna", "contactEmail": "<EMAIL>", "contactCompany": "The Cafe Luna", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-11T21:16:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.634Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "b6747e1d-271c-4f94-a0d0-fad35fe26581", "contactId": "27444ed2-f5fc-4109-93e8-17d7a161060c", "contactName": "Troublemaker Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Troublemaker Cafe", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-11T23:07:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.634Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "8ef0191f-f7e5-46cd-8836-125b2f0b21a1", "contactId": "42f89a2d-c35e-4e87-86c6-d9e74031e6a2", "contactName": "Stauf's Coffee", "contactEmail": "<EMAIL>", "contactCompany": "Stauf's Coffee", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T00:26:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.635Z", "updatedAt": "2025-09-08T20:14:47.580Z"}, {"id": "f0c5eea9-81b2-4313-bd3f-baf55cfccb54", "contactId": "9e9a3da1-0d0a-4bdb-ba19-396e50329b75", "contactName": "Rothrock Coffee", "contactEmail": "<EMAIL>", "contactCompany": "Rothrock Coffee", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.635Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "7c5ae7b4-c0c8-4ffb-ba13-ab37edb2304c", "contactId": "34f69daa-8fb2-4b00-940d-39d16141edc3", "contactName": "Patron Saint Cafe + Bar", "contactEmail": "<EMAIL>", "contactCompany": "Patron Saint Cafe + Bar", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T12:58:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.635Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "d36a6d6c-e29e-4a56-887a-b3f4a1871f27", "contactId": "1696cf18-f31f-4672-8b9e-1c4331a8376d", "contactName": "Monigram Coffee Roasters", "contactEmail": "<EMAIL>", "contactCompany": "Monigram Coffee Roasters", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T14:11:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.636Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "44343537-a332-415a-b514-2eaebadb4422", "contactId": "926af4fd-5f2b-4b20-a830-3308f6deb792", "contactName": "Matter of Taste: Coffee Bar", "contactEmail": "<EMAIL>", "contactCompany": "Matter of Taste: Coffee Bar", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T15:47:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.636Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "19df16f6-b35d-4e9a-be0e-969f6944e3ec", "contactId": "f4ca6358-0a90-46ec-af8f-9023722ee683", "contactName": "Kitty Brew Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Kitty Brew Cafe", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T16:54:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.637Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "03e5d0c4-2b1a-4540-a4cf-b9b0e1f352fb", "contactId": "8790d7f5-cb20-4f08-a146-f423dcca17e3", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fox Meadows Creamery, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T18:46:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.637Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "dd1b0451-7edd-41ad-8da2-85572f77590f", "contactId": "1acafafa-ebfc-4603-bfaf-6487339b845a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fox Meadows Creamery, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T20:03:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.638Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "27e2f686-090b-4c38-8c93-ac51804ef09d", "contactId": "ab963732-f7ab-4062-9528-5aa160b1f3a0", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Elizabethtown Coffee Company", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T21:56:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.639Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "500eaca9-f775-45d9-8ceb-c519d3748d08", "contactId": "ef6d11e4-5cdc-4c8a-a3ae-ebf1703d89d7", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-12T23:44:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.640Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "18632387-5e12-4c2f-b4b6-1a19188ee876", "contactId": "b7c38229-4ef0-4c53-a3d6-32c43e59fca4", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T01:43:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.640Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "2e23f380-4116-4457-b4c3-cab6008b09dd", "contactId": "ce9396ad-e0c9-4a65-806a-cc3a65e32502", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Toasted and Roasted", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.641Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "7bc5726c-71ba-46ba-b0bd-4d3922de669a", "contactId": "48c94450-2429-4810-90f5-eea0e3531964", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Bewley Irish Imports", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T12:43:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.641Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "31099f52-f517-4c8e-b5f3-d301aa3e8d7a", "contactId": "d477c416-6ad1-4bcb-bafb-d9c192bdc857", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T14:33:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.642Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "eee7dafb-c4ff-4e6c-b822-bf27aed9452e", "contactId": "f5d8e935-5b14-427b-b705-3afd4c38adf3", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Albertsons Companies", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:39:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.642Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "3b6bc71e-42d3-4f86-a005-26e7018c3f07", "contactId": "99252a7a-8ca5-47f5-82d1-826412f1ba1a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Brown's Super Stores, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T16:53:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.643Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "54037dc2-563a-4483-80d1-6f31f972b184", "contactId": "f95cb6e0-ab09-4b10-8681-8c5bf457210a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T18:03:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.643Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "5a01be8b-d893-49b3-a5f9-fc217b0c655c", "contactId": "f82ffbca-1eca-49be-9216-e8f92e629280", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Wakefern Food Corp.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T19:11:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.643Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "1a50bca2-c3c3-439f-8939-48790eaa35dc", "contactId": "8080e396-9b49-4494-8385-a39c9a59f249", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T20:59:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.644Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "494b0210-db75-4900-a651-76ec1d77adc4", "contactId": "da1f0ba1-d34a-45f9-ad6e-806ecdfe9ab9", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Dollar General", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T22:51:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.644Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "6518fb39-2e7f-458d-94ee-a3c43a9c2696", "contactId": "00c5d6aa-c573-4fdc-b07e-ac5967602b8c", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>'s Company", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T00:44:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.645Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "d9d2a0d2-a067-446f-b540-e71240411473", "contactId": "83cf39bf-d8dd-4735-a1f2-adddc5863186", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Wakefern Food Corp.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.646Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "9e8c7aa0-8384-4cc5-92a1-51189c46f11d", "contactId": "9f82ea5b-3631-4506-b1d4-2f971bf0ba7f", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T12:09:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.647Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "70bd917b-b50c-476f-a0ec-e97a7c8779c8", "contactId": "864a0796-515d-44b7-ac9e-0129fe162743", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Walmart", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T14:01:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.647Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "9ce2d414-6335-4e40-9277-1ed272f934e1", "contactId": "e236df63-e6ee-4236-b589-e154e4bebd62", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON>s", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T15:22:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.648Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "0ae3aad8-d432-4ae8-9eab-4b5903615469", "contactId": "a498cb34-dd27-41b1-adcc-b20308bb2f15", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T16:41:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.649Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "5e215954-4ba0-41f4-9ede-64ea50714106", "contactId": "4b3abd2d-70e6-431f-a8d7-3cd666e55bde", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Albertsons Companies", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T18:09:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.649Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "7ba9055c-e128-492f-9cba-dd0eb5c0761c", "contactId": "ffe8a079-0ba7-489b-9ccf-054c9125934e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Whole Foods Market", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T19:11:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.650Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "955313d6-c55b-425c-9dd6-2da70e54d925", "contactId": "addd7d1f-f2ef-4b22-8a91-a809f4d4c5ca", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T20:14:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.650Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "b0327c12-ea77-4f91-b85d-fd1afd2c6e6e", "contactId": "1bbe6dfd-8f2e-4a30-ad4e-07ac087619ef", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T22:10:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.650Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "221eea31-5ab6-4e29-8315-5ad4e46e51bb", "contactId": "b4371c4c-b720-4d3a-9693-d29bc3b0cc2b", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-14T23:20:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.650Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "beea3bea-8fe2-4b9e-9399-b987c9360a9e", "contactId": "90ca84f3-005a-4e81-a25e-cbd79c2e5979", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-15T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.651Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "0877bc72-4c30-4f8c-a2f3-4fdfb1ab2e6e", "contactId": "5a05fe8e-d191-46cd-a738-2b9f219fe252", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-15T12:02:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.651Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "2133db48-cb3f-48d6-866f-96c8ef573f45", "contactId": "3b79c30a-e83c-4bfb-9f14-cf1ef3830f24", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-15T13:19:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.652Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "d6a55e95-e823-42a8-8d58-51a8fa3cafc3", "contactId": "496235fb-c624-4457-86e1-2981a4079ab1", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Weis Markets", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-15T14:36:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.652Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "3b029a9c-a5f4-4487-97ea-003500c2eaaf", "contactId": "43b6efed-9612-496d-b13e-b510594daa73", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-15T16:21:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.653Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "92888632-d1e7-4ce7-8f50-84ad2e838150", "contactId": "8474035a-613a-4b81-bd0d-b0555934d79f", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Whole Foods Market", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-15T17:33:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.654Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "78459ec7-ccf3-44e7-a13b-a7592235f87d", "contactId": "206940fd-3406-49f1-81f0-495d181c08d1", "contactName": "<PERSON>", "contactEmail": "michael.ma<PERSON><PERSON>@gianteagle.com", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-15T19:22:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.655Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "5b703d4b-77d7-4cdf-b567-cf1aee910a65", "contactId": "b90558dd-9055-48a7-ab0a-21314e2f9c37", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Sam's Club", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-15T20:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.656Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "2e5b5f8c-6351-48d1-8edc-dd41d9a33020", "contactId": "0bc7e3c6-4988-40e9-9050-f25eab38a638", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-15T22:49:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.656Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "c358a515-6283-41b9-b63f-69d99e1e566a", "contactId": "1d79cb72-23c4-4c21-88d3-96684b1eae0b", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-16T00:25:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.657Z", "updatedAt": "2025-09-08T20:14:47.581Z"}, {"id": "466dda2a-0b69-49e4-860c-02fbc9cbcaa6", "contactId": "2b413848-67ac-4748-9e4e-9d7e37c2c137", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "OTR", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-16T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.658Z", "updatedAt": "2025-09-08T20:14:47.582Z"}, {"id": "0b8b5a1c-5bbc-4bf8-a782-4b16e0c15b57", "contactId": "ca668847-f95e-4bfa-86b3-a92f87b78c07", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Leg Up Farmers Market", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-16T12:40:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.658Z", "updatedAt": "2025-09-08T20:14:47.582Z"}, {"id": "7735b81d-8bca-406d-b377-d666c9b385ee", "contactId": "058d6ea7-9a70-4d4e-8df6-e50323744978", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "BJ's Wholesale Club", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-16T13:44:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.659Z", "updatedAt": "2025-09-08T20:14:47.582Z"}, {"id": "f97beb8f-4105-466b-b53e-5fb14bbbce0c", "contactId": "e0c6233b-385e-418c-94c3-83b454f88d5d", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "RITE AID", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-16T15:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.659Z", "updatedAt": "2025-09-08T20:14:47.582Z"}, {"id": "1aa554ac-3410-4998-b062-c5f7b01c7388", "contactId": "5b44a2af-ce0e-4b51-b052-e709c8073e8d", "contactName": "Victoria Basham", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-16T16:24:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:09:28.660Z", "updatedAt": "2025-09-08T20:14:47.582Z"}]