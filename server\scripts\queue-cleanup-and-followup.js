const path = require('path');
const { readJsonFile, writeJsonFile } = require('../utils/dataUtils');
const EmailQueue = require('../models/EmailQueue');

// File paths
const EMAIL_QUEUE_FILE = path.join(__dirname, '../../data/emailQueue.json');
const CONVERSATIONS_FILE = path.join(__dirname, '../../data/conversations.json');

/**
 * Clean up failed contacts and schedule follow-ups for successful ones
 */
async function cleanupQueueAndScheduleFollowups() {
  try {
    console.log('🧹 Starting queue cleanup and follow-up scheduling...\n');

    // Load current data
    const queue = await readJsonFile(EMAIL_QUEUE_FILE);
    const conversations = await readJsonFile(CONVERSATIONS_FILE);

    console.log(`📊 Current queue status:`);
    console.log(`   Total entries: ${queue.length}`);
    
    // Analyze current queue
    const stats = {
      total: queue.length,
      sent: queue.filter(q => q.status === 'sent').length,
      failed: queue.filter(q => q.status === 'failed').length,
      queued: queue.filter(q => q.status === 'queued').length,
      processing: queue.filter(q => q.status === 'processing').length
    };

    console.log(`   Sent: ${stats.sent}`);
    console.log(`   Failed: ${stats.failed}`);
    console.log(`   Queued: ${stats.queued}`);
    console.log(`   Processing: ${stats.processing}\n`);

    // Find contacts who have received replies
    const contactsWithReplies = new Set();
    conversations.forEach(conv => {
      if (conv.direction === 'received') {
        contactsWithReplies.add(conv.contactId);
      }
    });

    console.log(`💬 Found ${contactsWithReplies.size} contacts who have already replied\n`);

    // Step 1: Remove failed entries
    const failedEntries = queue.filter(q => q.status === 'failed');
    const cleanQueue = queue.filter(q => q.status !== 'failed');

    console.log(`🗑️  Removing ${failedEntries.length} failed entries:`);
    failedEntries.forEach(entry => {
      console.log(`   - ${entry.contactName} (${entry.contactEmail}): ${entry.errorMessage}`);
    });

    // Step 2: Find successful contacts who need follow-ups
    const sentEntries = cleanQueue.filter(q => q.status === 'sent');
    const needsFollowup = sentEntries.filter(entry => {
      // Don't send follow-up if they already replied
      if (contactsWithReplies.has(entry.contactId)) {
        return false;
      }
      
      // Check if there's already a follow-up queued for this contact
      const hasFollowup = cleanQueue.some(q => 
        q.contactId === entry.contactId && 
        q.emailType === 'follow_up' && 
        (q.status === 'queued' || q.status === 'sent')
      );
      
      return !hasFollowup;
    });

    console.log(`\n📧 Contacts needing follow-ups: ${needsFollowup.length}`);
    console.log(`📧 Contacts with replies (skipping): ${sentEntries.length - needsFollowup.length - (sentEntries.length - sentEntries.filter(e => !contactsWithReplies.has(e.contactId)).length)}`);

    // Step 3: Create follow-up entries
    const followupEntries = [];
    const followupDelayDays = 5; // Send follow-up 5 days after initial email

    for (const entry of needsFollowup) {
      const initialSentDate = new Date(entry.sentAt);
      const followupDate = new Date(initialSentDate);
      followupDate.setDate(followupDate.getDate() + followupDelayDays);
      
      // If the follow-up date is in the past, schedule it for tomorrow
      const now = new Date();
      if (followupDate < now) {
        followupDate.setDate(now.getDate() + 1);
        followupDate.setHours(9, 0, 0, 0); // 9 AM tomorrow
      }

      const followupEntry = new EmailQueue({
        contactId: entry.contactId,
        contactName: entry.contactName,
        contactEmail: entry.contactEmail,
        contactCompany: entry.contactCompany,
        emailType: 'follow_up',
        tone: entry.tone,
        priority: 'normal',
        scheduledFor: followupDate.toISOString()
      });

      followupEntries.push(followupEntry.toJSON());
    }

    // Step 4: Create the new queue
    const newQueue = [...cleanQueue, ...followupEntries];

    // Step 5: Save the updated queue
    await writeJsonFile(EMAIL_QUEUE_FILE, newQueue);

    // Summary
    console.log(`\n✅ Queue cleanup and follow-up scheduling complete!`);
    console.log(`\n📊 Summary:`);
    console.log(`   Removed failed entries: ${failedEntries.length}`);
    console.log(`   Added follow-up entries: ${followupEntries.length}`);
    console.log(`   New queue size: ${newQueue.length}`);
    console.log(`   Contacts with replies (skipped): ${contactsWithReplies.size}`);

    console.log(`\n📅 Follow-up schedule preview (first 10):`);
    followupEntries.slice(0, 10).forEach(entry => {
      const scheduleDate = new Date(entry.scheduledFor);
      console.log(`   - ${entry.contactName}: ${scheduleDate.toLocaleDateString()} ${scheduleDate.toLocaleTimeString()}`);
    });

    if (followupEntries.length > 10) {
      console.log(`   ... and ${followupEntries.length - 10} more`);
    }

    console.log(`\n🎯 Next steps:`);
    console.log(`   1. The email queue processor will automatically send follow-ups`);
    console.log(`   2. Check the dashboard to monitor progress`);
    console.log(`   3. Review any replies that come in`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  cleanupQueueAndScheduleFollowups()
    .then(() => {
      console.log('\n🎉 Script completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = { cleanupQueueAndScheduleFollowups };
