[{"id": "907b7bda-3300-4253-9615-b9b7841bff70", "contactId": "946f7307-9d65-47a2-a749-84ed3490deff", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "The Novel <PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-20T20:18:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-20T20:18:13.674Z", "createdAt": "2025-08-20T20:04:00.027Z", "updatedAt": "2025-08-20T20:18:13.675Z"}, {"id": "0ee769d1-42ce-4d66-87e2-0c789a6557ae", "contactId": "8d243ee7-3f93-4fac-a50a-b7cba26782e2", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Char & Stave", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-20T21:30:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-20T22:18:36.471Z", "createdAt": "2025-08-20T20:04:05.059Z", "updatedAt": "2025-08-20T22:18:36.472Z"}, {"id": "d83b3d78-0217-4d66-821e-0695ab31b78a", "contactId": "675cf342-a462-48ad-a658-98f82ef66cf9", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "CraveWell Juicery & Cafe", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-20T23:16:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-20T23:16:35.247Z", "createdAt": "2025-08-20T20:04:07.115Z", "updatedAt": "2025-08-20T23:16:35.248Z"}, {"id": "d293d286-94b3-4638-808f-078c3d4c3fda", "contactId": "dfa50acc-692b-4be5-acff-60745e35a8e8", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Cake Life Bake Shop", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-21T00:42:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-21T01:38:04.645Z", "createdAt": "2025-08-20T20:04:17.083Z", "updatedAt": "2025-08-21T01:38:04.648Z"}, {"id": "0e609316-c5e1-4d0e-bf8a-845048a6ac34", "contactId": "f432bada-e377-4667-bde3-b784429841cb", "contactName": "Aveley Farms Coffee Roasters", "contactEmail": "<EMAIL>", "contactCompany": "Aveley Farms Coffee Roasters", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-21T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-21T14:13:14.187Z", "createdAt": "2025-08-20T20:10:44.229Z", "updatedAt": "2025-08-21T14:13:14.188Z"}, {"id": "49045459-d814-445e-815f-cf1db18be1f2", "contactId": "36e78e5e-7bd9-4a0a-86e3-84d6e2445a77", "contactName": "Blue Brew Coffee Shop", "contactEmail": "<EMAIL>", "contactCompany": "Blue Brew Coffee Shop", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-21T15:47:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-21T16:25:18.026Z", "createdAt": "2025-08-20T20:10:50.518Z", "updatedAt": "2025-08-21T16:25:18.027Z"}, {"id": "55336de7-e9e8-4a4a-93fa-b185124f91db", "contactId": "c0bd0c29-3b29-44fd-adb8-c16cb3bd0c06", "contactName": "Green Line Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Green Line Cafe", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-21T19:05:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-21T19:41:48.779Z", "createdAt": "2025-08-20T20:10:54.760Z", "updatedAt": "2025-08-21T19:41:48.791Z"}, {"id": "ea293c56-1814-4361-9021-1e9d821fd8bd", "contactId": "ac37c2b9-0eab-4bc6-8d57-77a0c4735e9b", "contactName": "Devon Lawrence", "contactEmail": "<EMAIL>", "contactCompany": "Old Trail Tavern", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:00:28.569Z", "createdAt": "2025-08-20T20:11:14.601Z", "updatedAt": "2025-08-23T13:00:28.570Z"}, {"id": "228a2a89-24f6-408e-b2aa-0c45dcd43750", "contactId": "e512c5b3-1227-47ab-ae9c-0a3cfb32ef30", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "DellaVino Imports, LLC", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T14:30:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:01:28.849Z", "createdAt": "2025-08-20T20:11:17.590Z", "updatedAt": "2025-08-23T13:01:28.850Z"}, {"id": "08c45045-0d72-434c-b55a-51614f226720", "contactId": "2820f0b6-bb0a-4198-a8dd-fbc9181c34ef", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Stove & Co Restaurant Group", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T17:41:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:02:29.415Z", "createdAt": "2025-08-20T20:11:23.530Z", "updatedAt": "2025-08-23T13:02:29.416Z"}, {"id": "0c79ffa8-80c8-4ef8-b7e4-c81e2d8b7c33", "contactId": "1eeb28a0-f9f2-4737-9d04-1fbd4e6502a4", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "The Flintridge Proper Neighborhood Restaurant & Bar", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T18:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:03:29.175Z", "createdAt": "2025-08-20T20:11:28.044Z", "updatedAt": "2025-08-23T13:03:29.180Z"}, {"id": "011819d9-f798-4d55-aa0c-f4e6ae6910d6", "contactId": "097f5433-5264-4373-89d5-3c93a86ebd93", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Baron <PERSON> LLC", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-22T20:10:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-23T13:04:26.847Z", "createdAt": "2025-08-20T20:11:32.116Z", "updatedAt": "2025-08-23T13:04:26.851Z"}, {"id": "7693c1dc-37de-4ddc-a635-d0289ada2c19", "contactId": "878b68bf-657d-43db-bc98-cba1eddb342f", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "m2o Burgers & Salads", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-25T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-25T13:42:00.551Z", "createdAt": "2025-08-20T20:11:38.755Z", "updatedAt": "2025-08-25T13:42:00.572Z"}, {"id": "263f3789-1f9c-4767-8afe-b825f2fb72d9", "contactId": "52aa1f53-235e-4d90-a971-a9d5d7b3ac98", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-25T15:27:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-25T15:27:26.839Z", "createdAt": "2025-08-20T20:11:58.260Z", "updatedAt": "2025-08-25T15:27:26.840Z"}, {"id": "58766974-acd1-47bd-a4e8-a4b5c1e67326", "contactId": "bb1df581-fcd2-4b8f-aa51-c50b3649b00e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-25T18:34:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-25T18:35:06.355Z", "createdAt": "2025-08-20T20:12:02.014Z", "updatedAt": "2025-08-25T18:35:06.357Z"}, {"id": "0be1e1c3-d66b-4b7c-a50d-3971b680fcfb", "contactId": "463e0fcf-67c5-486f-891e-ac7f5232a2b2", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-26T16:12:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-26T16:41:13.283Z", "createdAt": "2025-08-20T20:12:11.964Z", "updatedAt": "2025-08-26T16:41:13.295Z"}, {"id": "fa191683-b5e1-49bb-a7d5-aed8dd82d819", "contactId": "60b98b92-953b-40fe-a568-64cbd435bcf7", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-26T17:33:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-26T19:59:12.553Z", "createdAt": "2025-08-20T20:12:14.534Z", "updatedAt": "2025-08-26T19:59:12.555Z"}, {"id": "379e4046-9552-4154-91d9-533b949af3e0", "contactId": "00cc4457-8472-4d93-a93d-3c7f099198c5", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-26T20:06:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-27T00:10:04.317Z", "createdAt": "2025-08-20T20:12:17.929Z", "updatedAt": "2025-08-27T00:10:04.367Z"}, {"id": "10d660fc-654a-46de-980b-4eb72e1b2e85", "contactId": "c9ee3ac3-2c44-44c3-a953-5dab3f1ab972", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Freshido", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-27T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-27T14:14:19.167Z", "createdAt": "2025-08-20T20:12:32.885Z", "updatedAt": "2025-08-27T14:14:19.171Z"}, {"id": "bb1e73b5-6a3c-48aa-b33d-c70529225bcc", "contactId": "7c6c05a3-abe9-4e3c-baa9-560b8cab0410", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fleetwood Foods LLC", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-27T15:55:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-27T15:55:47.022Z", "createdAt": "2025-08-20T20:12:42.348Z", "updatedAt": "2025-08-27T15:55:47.025Z"}, {"id": "ef6bcebd-c888-4030-b510-5fd8c848752a", "contactId": "d947fd3d-d937-4e61-8649-294c83407eca", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Hakan <PERSON>han Restaurants", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-27T18:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-27T18:59:11.658Z", "createdAt": "2025-08-20T20:12:44.244Z", "updatedAt": "2025-08-27T18:59:11.660Z"}, {"id": "7cd7407e-f9e0-4c64-84aa-476f2c1ffbdd", "contactId": "ea8149b0-d971-431c-bad7-02d0cb8dc768", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Conshy Girls Restaurant Group", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-27T23:59:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T01:02:41.482Z", "createdAt": "2025-08-23T11:51:34.899Z", "updatedAt": "2025-08-28T01:02:41.483Z"}, {"id": "1be35b96-db03-485d-833c-d9ac99c4c2cf", "contactId": "0f7db24c-b606-4aff-809a-c8048ca33fad", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "AWSM Sauce Co", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T01:22:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T01:22:44.054Z", "createdAt": "2025-08-23T11:51:41.959Z", "updatedAt": "2025-08-28T01:22:44.056Z"}, {"id": "f9d280b5-598a-43ee-868b-17837c68c90e", "contactId": "11b50445-477a-46be-b0bb-5cc975b56a93", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T16:01:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T18:11:36.010Z", "createdAt": "2025-08-23T11:52:23.795Z", "updatedAt": "2025-08-28T18:11:36.012Z"}, {"id": "cce0657c-c21a-4bb1-8e77-6a5ebf88f055", "contactId": "384daffa-097d-439d-9526-a481fd0a9d1b", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T18:36:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T18:36:30.621Z", "createdAt": "2025-08-23T11:52:31.620Z", "updatedAt": "2025-08-28T18:36:30.622Z"}, {"id": "7f665403-9142-4923-913d-db4db824a405", "contactId": "2eddba57-e13a-4b85-a579-4a89aae9c0fc", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T21:21:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T21:21:19.862Z", "createdAt": "2025-08-23T11:52:34.983Z", "updatedAt": "2025-08-28T21:21:19.864Z"}, {"id": "a50a7d60-45ce-4ad0-b647-2b371f698d8d", "contactId": "ab7fba09-1f69-4996-a469-854b3e0d0b7e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-28T23:10:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-28T23:25:32.017Z", "createdAt": "2025-08-23T11:52:36.993Z", "updatedAt": "2025-08-28T23:25:32.052Z"}, {"id": "71c97eb6-3001-4d62-8cd2-75217de604b9", "contactId": "877ab8a5-6592-449d-ae0a-6f07a57eae7c", "contactName": "The Cafe Luna", "contactEmail": "<EMAIL>", "contactCompany": "The Cafe Luna", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-29T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-29T17:02:43.779Z", "createdAt": "2025-08-23T11:52:57.275Z", "updatedAt": "2025-08-29T17:02:43.781Z"}, {"id": "6098a3b1-bdf3-4cc8-8e26-dcd98e4d43d1", "contactId": "27444ed2-f5fc-4109-93e8-17d7a161060c", "contactName": "Troublemaker Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Troublemaker Cafe", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-29T14:55:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-29T17:03:39.967Z", "createdAt": "2025-08-23T11:52:59.582Z", "updatedAt": "2025-08-29T17:03:39.968Z"}, {"id": "6a245601-0989-477b-9494-c8275c61d1ab", "contactId": "42f89a2d-c35e-4e87-86c6-d9e74031e6a2", "contactName": "Stauf's Coffee", "contactEmail": "<EMAIL>", "contactCompany": "Stauf's Coffee", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-29T16:14:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-08-29T17:08:47.960Z", "createdAt": "2025-08-23T11:53:02.335Z", "updatedAt": "2025-08-29T17:08:47.962Z"}, {"id": "472204f1-5d21-4e69-91d0-0d9d0f4a3104", "contactId": "9e9a3da1-0d0a-4bdb-ba19-396e50329b75", "contactName": "Rothrock Coffee", "contactEmail": "<EMAIL>", "contactCompany": "Rothrock Coffee", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-29T23:12:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:04:36.348Z", "createdAt": "2025-08-23T11:53:11.822Z", "updatedAt": "2025-09-01T15:04:36.376Z"}, {"id": "5a8155ce-d07b-4217-88f0-0aa19e31dbac", "contactId": "34f69daa-8fb2-4b00-940d-39d16141edc3", "contactName": "Patron Saint Cafe + Bar", "contactEmail": "<EMAIL>", "contactCompany": "Patron Saint Cafe + Bar", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T01:46:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:05:29.342Z", "createdAt": "2025-08-23T11:53:14.315Z", "updatedAt": "2025-09-01T15:05:29.444Z"}, {"id": "fb04656e-42f0-4f1b-aef0-3151830653e5", "contactId": "1696cf18-f31f-4672-8b9e-1c4331a8376d", "contactName": "Monigram Coffee Roasters", "contactEmail": "<EMAIL>", "contactCompany": "Monigram Coffee Roasters", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:06:33.619Z", "createdAt": "2025-08-23T11:53:16.472Z", "updatedAt": "2025-09-01T15:06:33.627Z"}, {"id": "28b243a7-159a-4bc3-81fe-4ad914dfd90a", "contactId": "926af4fd-5f2b-4b20-a830-3308f6deb792", "contactName": "Matter of Taste: Coffee Bar", "contactEmail": "<EMAIL>", "contactCompany": "Matter of Taste: Coffee Bar", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T14:35:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:07:29.433Z", "createdAt": "2025-08-23T11:53:20.921Z", "updatedAt": "2025-09-01T15:07:29.434Z"}, {"id": "8f268ef1-3e8c-4fe7-b90d-d8c2edbfd5db", "contactId": "f4ca6358-0a90-46ec-af8f-9023722ee683", "contactName": "Kitty Brew Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Kitty Brew Cafe", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T16:57:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:08:29.881Z", "createdAt": "2025-08-23T11:53:25.189Z", "updatedAt": "2025-09-01T15:08:29.883Z"}, {"id": "300d5b78-67e2-4b17-9d14-4670ec9398dd", "contactId": "8790d7f5-cb20-4f08-a146-f423dcca17e3", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fox Meadows Creamery, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T19:51:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:09:27.240Z", "createdAt": "2025-08-27T00:54:48.124Z", "updatedAt": "2025-09-01T15:09:27.241Z"}, {"id": "9bf5c0ab-f170-4bba-9d44-101b1424e640", "contactId": "1acafafa-ebfc-4603-bfaf-6487339b845a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fox Meadows Creamery, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-30T22:34:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:10:25.222Z", "createdAt": "2025-08-27T00:54:51.837Z", "updatedAt": "2025-09-01T15:10:25.225Z"}, {"id": "3fb66482-d768-4178-a455-5a567b9d280f", "contactId": "ab963732-f7ab-4062-9528-5aa160b1f3a0", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Elizabethtown Coffee Company", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-31T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T15:11:29.220Z", "createdAt": "2025-08-27T00:54:55.684Z", "updatedAt": "2025-09-01T15:11:29.221Z"}, {"id": "7e10587e-43dc-48d6-b807-16c47efda048", "contactId": "ef6d11e4-5cdc-4c8a-a3ae-ebf1703d89d7", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-31T16:59:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T17:13:01.411Z", "createdAt": "2025-08-27T00:55:01.246Z", "updatedAt": "2025-09-01T17:13:01.412Z"}, {"id": "d0c19475-fc36-4b47-a249-5adca9da0e40", "contactId": "b7c38229-4ef0-4c53-a3d6-32c43e59fca4", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-31T21:18:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-01T17:14:01.319Z", "createdAt": "2025-08-27T00:55:04.881Z", "updatedAt": "2025-09-01T17:14:01.320Z"}, {"id": "162893b4-176b-4073-a5bb-6a7b29a783fc", "contactId": "ce9396ad-e0c9-4a65-806a-cc3a65e32502", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Toasted and Roasted", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T00:21:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:24:21.569Z", "createdAt": "2025-08-27T00:55:07.507Z", "updatedAt": "2025-09-02T17:24:21.587Z"}, {"id": "643c2d90-96f8-4254-bc08-48dbe92f8029", "contactId": "48c94450-2429-4810-90f5-eea0e3531964", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Bewley Irish Imports", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:25:16.931Z", "createdAt": "2025-08-27T00:55:09.907Z", "updatedAt": "2025-09-02T17:25:16.937Z"}, {"id": "55b67259-5c7b-4f5e-a518-bd60c1cedfa7", "contactId": "d477c416-6ad1-4bcb-bafb-d9c192bdc857", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T14:25:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:26:15.114Z", "createdAt": "2025-08-29T20:33:18.066Z", "updatedAt": "2025-09-02T17:26:15.115Z"}, {"id": "3140c4c5-64d0-4f32-9b37-374b61844d5e", "contactId": "f5d8e935-5b14-427b-b705-3afd4c38adf3", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Albertsons Companies", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T17:05:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:27:15.563Z", "createdAt": "2025-08-29T20:33:21.883Z", "updatedAt": "2025-09-02T17:27:15.564Z"}, {"id": "6b42ee20-c15b-4fc2-98a0-80e7aa9a4c94", "contactId": "99252a7a-8ca5-47f5-82d1-826412f1ba1a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Brown's Super Stores, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T19:27:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:28:17.733Z", "createdAt": "2025-08-29T20:33:25.532Z", "updatedAt": "2025-09-02T17:28:17.734Z"}, {"id": "f6d49958-a708-4293-a669-6a21b400b42b", "contactId": "f95cb6e0-ab09-4b10-8681-8c5bf457210a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-01T22:56:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:29:13.899Z", "createdAt": "2025-08-29T20:33:29.361Z", "updatedAt": "2025-09-02T17:29:13.902Z"}, {"id": "c2f3fb37-7f8f-472e-a0fd-58865972007c", "contactId": "f82ffbca-1eca-49be-9216-e8f92e629280", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Wakefern Food Corp.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T01:35:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:30:15.862Z", "createdAt": "2025-08-29T20:33:32.008Z", "updatedAt": "2025-09-02T17:30:15.863Z"}, {"id": "6dbeaae9-3c96-4ed9-a628-91c483037509", "contactId": "8080e396-9b49-4494-8385-a39c9a59f249", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:31:16.522Z", "createdAt": "2025-08-29T20:33:33.790Z", "updatedAt": "2025-09-02T17:31:16.525Z"}, {"id": "5cdc2a0d-3199-43f3-9030-accd4cf352a1", "contactId": "da1f0ba1-d34a-45f9-ad6e-806ecdfe9ab9", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Dollar General", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T13:26:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:32:15.466Z", "createdAt": "2025-08-29T20:33:36.562Z", "updatedAt": "2025-09-02T17:32:15.468Z"}, {"id": "b3f7c77e-9866-462d-8fb4-4b13c7591ab9", "contactId": "00c5d6aa-c573-4fdc-b07e-ac5967602b8c", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>'s Company", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T16:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-02T17:33:16.289Z", "createdAt": "2025-08-29T20:33:38.561Z", "updatedAt": "2025-09-02T17:33:16.290Z"}, {"id": "c900ad9d-74fc-4a2a-b2a9-151cdd991d83", "contactId": "83cf39bf-d8dd-4735-a1f2-adddc5863186", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Wakefern Food Corp.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T21:32:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:01:37.691Z", "createdAt": "2025-08-29T20:33:43.336Z", "updatedAt": "2025-09-03T18:01:37.693Z"}, {"id": "e36d2da8-e049-41f4-9307-1eb31cfba9a3", "contactId": "9f82ea5b-3631-4506-b1d4-2f971bf0ba7f", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-02T23:45:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:02:38.809Z", "createdAt": "2025-08-29T20:33:45.473Z", "updatedAt": "2025-09-03T18:02:38.813Z"}, {"id": "b0a21081-c8d2-4353-b42a-2dd38a95a26e", "contactId": "864a0796-515d-44b7-ac9e-0129fe162743", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Walmart", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-03T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:03:36.710Z", "createdAt": "2025-08-29T20:33:47.789Z", "updatedAt": "2025-09-03T18:03:36.711Z"}, {"id": "06c877bd-f4cf-4c54-a8d9-48aea4c4b3a3", "contactId": "e236df63-e6ee-4236-b589-e154e4bebd62", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON>s", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-03T13:11:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:04:39.890Z", "createdAt": "2025-08-29T20:33:50.230Z", "updatedAt": "2025-09-03T18:04:39.891Z"}, {"id": "edae6f8e-09b5-46e9-b54a-8f79b48eb0a5", "contactId": "a498cb34-dd27-41b1-adcc-b20308bb2f15", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-03T15:33:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:05:36.641Z", "createdAt": "2025-08-29T20:33:52.459Z", "updatedAt": "2025-09-03T18:05:36.642Z"}, {"id": "3f7a8d8b-df64-484e-abae-1b3cec4e98ca", "contactId": "4b3abd2d-70e6-431f-a8d7-3cd666e55bde", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Albertsons Companies", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-03T17:40:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-03T18:06:39.761Z", "createdAt": "2025-08-29T20:33:54.263Z", "updatedAt": "2025-09-03T18:06:39.762Z"}, {"id": "2f6f45e9-0e9f-4bf5-9867-2948881e6441", "contactId": "ffe8a079-0ba7-489b-9ccf-054c9125934e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Whole Foods Market", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-04T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-04T13:26:15.333Z", "createdAt": "2025-08-29T20:34:03.818Z", "updatedAt": "2025-09-04T13:26:15.335Z"}, {"id": "512181d0-3052-4959-9b39-684dd82e8995", "contactId": "addd7d1f-f2ef-4b22-8a91-a809f4d4c5ca", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-04T16:40:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-04T17:37:34.203Z", "createdAt": "2025-08-29T20:34:16.221Z", "updatedAt": "2025-09-04T17:37:34.226Z"}, {"id": "bc89422f-c263-4f30-a270-ce39f8769d86", "contactId": "1bbe6dfd-8f2e-4a30-ad4e-07ac087619ef", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-04T22:18:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:47:28.360Z", "createdAt": "2025-08-29T20:34:24.628Z", "updatedAt": "2025-09-05T19:47:28.398Z"}, {"id": "4abcc895-3bda-4c8b-87f6-169f02785b95", "contactId": "b4371c4c-b720-4d3a-9693-d29bc3b0cc2b", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T01:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:48:24.159Z", "createdAt": "2025-08-29T20:34:26.822Z", "updatedAt": "2025-09-05T19:48:24.174Z"}, {"id": "75e6a6ba-1640-4299-9813-738d57a2815f", "contactId": "90ca84f3-005a-4e81-a25e-cbd79c2e5979", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:49:23.866Z", "createdAt": "2025-08-29T20:34:29.318Z", "updatedAt": "2025-09-05T19:49:23.872Z"}, {"id": "2e83c6cd-786c-4619-bfb0-43b775b9a33d", "contactId": "5a05fe8e-d191-46cd-a738-2b9f219fe252", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T12:52:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:50:21.809Z", "createdAt": "2025-08-29T20:34:31.857Z", "updatedAt": "2025-09-05T19:50:21.814Z"}, {"id": "3d2e089c-675e-4e49-bb7f-90a33fac219d", "contactId": "3b79c30a-e83c-4bfb-9f14-cf1ef3830f24", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T16:22:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:51:23.855Z", "createdAt": "2025-08-29T20:34:33.789Z", "updatedAt": "2025-09-05T19:51:23.856Z"}, {"id": "e5bd5506-a70b-4048-9b7a-ef1d40d963c4", "contactId": "496235fb-c624-4457-86e1-2981a4079ab1", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Weis Markets", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T18:36:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T19:52:22.272Z", "createdAt": "2025-08-29T20:34:37.132Z", "updatedAt": "2025-09-05T19:52:22.273Z"}, {"id": "9fed376e-b5f0-4115-9d90-554248d2bfc2", "contactId": "43b6efed-9612-496d-b13e-b510594daa73", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-05T21:20:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-05T21:20:24.385Z", "createdAt": "2025-08-29T20:34:41.092Z", "updatedAt": "2025-09-05T21:20:24.386Z"}, {"id": "749073a2-4ce4-4fcc-bd65-aa4965587837", "contactId": "8474035a-613a-4b81-bd0d-b0555934d79f", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Whole Foods Market", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T00:32:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T14:59:20.192Z", "createdAt": "2025-08-29T20:34:42.753Z", "updatedAt": "2025-09-08T14:59:20.216Z"}, {"id": "37da050d-7300-4c91-b39c-f26b16b6d213", "contactId": "206940fd-3406-49f1-81f0-495d181c08d1", "contactName": "<PERSON>", "contactEmail": "michael.ma<PERSON><PERSON>@gianteagle.com", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:00:19.888Z", "createdAt": "2025-08-29T20:34:45.612Z", "updatedAt": "2025-09-08T15:00:19.892Z"}, {"id": "9138c239-82ae-42c3-8c35-d733b0f9e4e4", "contactId": "b90558dd-9055-48a7-ab0a-21314e2f9c37", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Sam's Club", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T13:32:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:01:12.420Z", "createdAt": "2025-08-29T20:34:48.580Z", "updatedAt": "2025-09-08T15:01:12.424Z"}, {"id": "8b1da15e-1cb3-4cad-99bb-62bfa37609c8", "contactId": "0bc7e3c6-4988-40e9-9050-f25eab38a638", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T16:22:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:02:14.474Z", "createdAt": "2025-08-29T20:34:50.745Z", "updatedAt": "2025-09-08T15:02:14.496Z"}, {"id": "d1fd5e30-a99b-476e-b7f4-b45ee020b390", "contactId": "1d79cb72-23c4-4c21-88d3-96684b1eae0b", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T18:50:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:03:16.496Z", "createdAt": "2025-08-29T20:34:56.565Z", "updatedAt": "2025-09-08T15:03:16.497Z"}, {"id": "4b35b01d-dd13-41ce-a698-f159da1277e3", "contactId": "2b413848-67ac-4748-9e4e-9d7e37c2c137", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "OTR", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-06T22:03:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:04:14.489Z", "createdAt": "2025-08-29T20:34:59.017Z", "updatedAt": "2025-09-08T15:04:14.491Z"}, {"id": "5619c3b1-b8df-4531-9d14-5f78a1047a86", "contactId": "ca8876db-9724-4995-8783-01ea9a9c7787", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T00:54:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:05:16.836Z", "createdAt": "2025-08-29T20:35:00.608Z", "updatedAt": "2025-09-08T15:05:16.837Z"}, {"id": "2ab3b68d-04e4-4c9e-90df-b01dfefdb693", "contactId": "ca668847-f95e-4bfa-86b3-a92f87b78c07", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Leg Up Farmers Market", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T11:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:06:15.963Z", "createdAt": "2025-08-29T20:35:03.329Z", "updatedAt": "2025-09-08T15:06:15.964Z"}, {"id": "1291e14e-d56e-4189-b847-1440c9416f62", "contactId": "058d6ea7-9a70-4d4e-8df6-e50323744978", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "BJ's Wholesale Club", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T13:58:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:07:12.884Z", "createdAt": "2025-08-29T20:35:08.097Z", "updatedAt": "2025-09-08T15:07:12.885Z"}, {"id": "5c27065d-afcf-4690-9120-75a2c5790bf9", "contactId": "e0c6233b-385e-418c-94c3-83b454f88d5d", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "RITE AID", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T16:19:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:08:13.601Z", "createdAt": "2025-08-29T20:35:10.507Z", "updatedAt": "2025-09-08T15:08:13.603Z"}, {"id": "8b43c13f-8165-41ea-97e8-73491b5a9fdc", "contactId": "5b44a2af-ce0e-4b51-b052-e709c8073e8d", "contactName": "Victoria Basham", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "initial_outreach", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-09-07T18:16:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T15:09:23.874Z", "createdAt": "2025-08-29T20:35:13.193Z", "updatedAt": "2025-09-08T15:09:23.875Z"}, {"id": "9b770758-aa41-4723-b198-0a9ce10a5201", "contactId": "946f7307-9d65-47a2-a749-84ed3490deff", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "The Novel <PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T20:03:52.612Z", "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:52.614Z"}, {"id": "35ba747e-4c9b-4683-b5f7-9427da33ccd5", "contactId": "8d243ee7-3f93-4fac-a50a-b7cba26782e2", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Char & Stave", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "sent", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": "2025-09-08T20:04:50.337Z", "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:04:50.339Z"}, {"id": "fe96dbda-5c95-4112-a4ac-1b6aff1b264e", "contactId": "675cf342-a462-48ad-a658-98f82ef66cf9", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "CraveWell Juicery & Cafe", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "092e2dec-1d7d-489f-b502-3fa1b7426bca", "contactId": "dfa50acc-692b-4be5-acff-60745e35a8e8", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Cake Life Bake Shop", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "99056024-a6de-4f95-8a6f-67fd45aaff44", "contactId": "f432bada-e377-4667-bde3-b784429841cb", "contactName": "Aveley Farms Coffee Roasters", "contactEmail": "<EMAIL>", "contactCompany": "Aveley Farms Coffee Roasters", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "42464342-7ab0-4695-aa09-857a4f4ef677", "contactId": "36e78e5e-7bd9-4a0a-86e3-84d6e2445a77", "contactName": "Blue Brew Coffee Shop", "contactEmail": "<EMAIL>", "contactCompany": "Blue Brew Coffee Shop", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "cdc9b1e4-d71e-41f1-9783-9ab50ab0a9c7", "contactId": "c0bd0c29-3b29-44fd-adb8-c16cb3bd0c06", "contactName": "Green Line Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Green Line Cafe", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "90c7c6b6-b7e9-4c35-a6c2-38479b553d54", "contactId": "ac37c2b9-0eab-4bc6-8d57-77a0c4735e9b", "contactName": "Devon Lawrence", "contactEmail": "<EMAIL>", "contactCompany": "Old Trail Tavern", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "60ae6645-a55d-4bf6-b39c-3ab674023c36", "contactId": "e512c5b3-1227-47ab-ae9c-0a3cfb32ef30", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "DellaVino Imports, LLC", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "dce36bd0-afc7-44ed-a32b-5c618f34b8e3", "contactId": "2820f0b6-bb0a-4198-a8dd-fbc9181c34ef", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Stove & Co Restaurant Group", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "1fc624fb-841e-41c3-a22f-c408c68045ab", "contactId": "1eeb28a0-f9f2-4737-9d04-1fbd4e6502a4", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "The Flintridge Proper Neighborhood Restaurant & Bar", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "67801278-d463-4b36-b4df-ef8c9ec35e87", "contactId": "097f5433-5264-4373-89d5-3c93a86ebd93", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Baron <PERSON> LLC", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "fabfc963-69f8-4a10-aa84-efbae138ac38", "contactId": "878b68bf-657d-43db-bc98-cba1eddb342f", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "m2o Burgers & Salads", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "2ecc1a11-c3c6-43ea-b4e8-6b4691e45a58", "contactId": "52aa1f53-235e-4d90-a971-a9d5d7b3ac98", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "c2eecf43-2176-4bf8-91d1-bc4a230096a6", "contactId": "bb1df581-fcd2-4b8f-aa51-c50b3649b00e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "d94923d4-966f-4183-a009-8a8e85f2f75c", "contactId": "463e0fcf-67c5-486f-891e-ac7f5232a2b2", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "9b4ad8b8-45d5-4cc0-9095-49b3e01db4a9", "contactId": "60b98b92-953b-40fe-a568-64cbd435bcf7", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "8b7933cb-c440-457e-9f39-67e659d5c1c4", "contactId": "00cc4457-8472-4d93-a93d-3c7f099198c5", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-08-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "0b7276a8-9c19-4787-8aa2-76c7a3f25f5c", "contactId": "c9ee3ac3-2c44-44c3-a953-5dab3f1ab972", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Freshido", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "8cf150fa-5973-4c4c-9198-4ec8cb107d82", "contactId": "7c6c05a3-abe9-4e3c-baa9-560b8cab0410", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fleetwood Foods LLC", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "f23f7a36-5fcc-4907-92f0-77fac0123860", "contactId": "d947fd3d-d937-4e61-8649-294c83407eca", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Hakan <PERSON>han Restaurants", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "44d656f6-03aa-474c-9c73-548a2e66a740", "contactId": "ea8149b0-d971-431c-bad7-02d0cb8dc768", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Conshy Girls Restaurant Group", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "7aac3689-4a1b-4024-ac17-b226083fc6cd", "contactId": "0f7db24c-b606-4aff-809a-c8048ca33fad", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "AWSM Sauce Co", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "e4337050-10ee-4689-9114-b8905fb84c88", "contactId": "11b50445-477a-46be-b0bb-5cc975b56a93", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "af2175a6-1ab7-4b56-94a4-956ad1941022", "contactId": "384daffa-097d-439d-9526-a481fd0a9d1b", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "4e3502d2-faf4-42d0-8a96-a005efaffa98", "contactId": "2eddba57-e13a-4b85-a579-4a89aae9c0fc", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "71cca977-dfb0-4fc6-8461-89e4c09aec80", "contactId": "ab7fba09-1f69-4996-a469-854b3e0d0b7e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "89313d83-e6d6-47ae-8441-57ba73a71a10", "contactId": "877ab8a5-6592-449d-ae0a-6f07a57eae7c", "contactName": "The Cafe Luna", "contactEmail": "<EMAIL>", "contactCompany": "The Cafe Luna", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "381ed8f0-f0d3-4f3e-b29b-518ecf634aed", "contactId": "27444ed2-f5fc-4109-93e8-17d7a161060c", "contactName": "Troublemaker Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Troublemaker Cafe", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "e42a8b48-f72c-4c09-a0dc-cf0735c2acb6", "contactId": "42f89a2d-c35e-4e87-86c6-d9e74031e6a2", "contactName": "Stauf's Coffee", "contactEmail": "<EMAIL>", "contactCompany": "Stauf's Coffee", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "3ededad8-0497-4662-9983-52aa01f07fa2", "contactId": "9e9a3da1-0d0a-4bdb-ba19-396e50329b75", "contactName": "Rothrock Coffee", "contactEmail": "<EMAIL>", "contactCompany": "Rothrock Coffee", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "35d23861-3095-4cf8-aea3-ae754eed678c", "contactId": "34f69daa-8fb2-4b00-940d-39d16141edc3", "contactName": "Patron Saint Cafe + Bar", "contactEmail": "<EMAIL>", "contactCompany": "Patron Saint Cafe + Bar", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "58c952bd-c72d-4b76-baf9-98371800b071", "contactId": "1696cf18-f31f-4672-8b9e-1c4331a8376d", "contactName": "Monigram Coffee Roasters", "contactEmail": "<EMAIL>", "contactCompany": "Monigram Coffee Roasters", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "79721361-cc76-4b27-a762-a5abc516a095", "contactId": "926af4fd-5f2b-4b20-a830-3308f6deb792", "contactName": "Matter of Taste: Coffee Bar", "contactEmail": "<EMAIL>", "contactCompany": "Matter of Taste: Coffee Bar", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "2ba4695c-b75b-42d7-948e-384f59bebc41", "contactId": "f4ca6358-0a90-46ec-af8f-9023722ee683", "contactName": "Kitty Brew Cafe", "contactEmail": "<EMAIL>", "contactCompany": "Kitty Brew Cafe", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "46f486d8-2b6f-4cc1-b0e7-6c6d36d94571", "contactId": "8790d7f5-cb20-4f08-a146-f423dcca17e3", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fox Meadows Creamery, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "d739b870-bef7-49ef-a48e-a830b6cf4150", "contactId": "1acafafa-ebfc-4603-bfaf-6487339b845a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Fox Meadows Creamery, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "db71f947-74af-4c3c-b11b-73ab39f47e42", "contactId": "ab963732-f7ab-4062-9528-5aa160b1f3a0", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Elizabethtown Coffee Company", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "eab769d5-c8b0-4f47-bba9-7517c9a8fb9c", "contactId": "ef6d11e4-5cdc-4c8a-a3ae-ebf1703d89d7", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "593381d1-fe9b-42be-acd6-99d42b80c9e7", "contactId": "b7c38229-4ef0-4c53-a3d6-32c43e59fca4", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "d2ee1c4b-8fbd-44e3-83d4-4be364463e8b", "contactId": "ce9396ad-e0c9-4a65-806a-cc3a65e32502", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Toasted and Roasted", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "aa7771c1-aab9-4a5d-bc31-1ac4fba3fde0", "contactId": "48c94450-2429-4810-90f5-eea0e3531964", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Bewley Irish Imports", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "31021d49-78cd-49a4-ad55-1348756fb300", "contactId": "d477c416-6ad1-4bcb-bafb-d9c192bdc857", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "5db4bfb2-068e-4c5a-a1c1-24bce7360620", "contactId": "f5d8e935-5b14-427b-b705-3afd4c38adf3", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Albertsons Companies", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "129568bc-60e5-4ae0-b0e4-0e465260b168", "contactId": "99252a7a-8ca5-47f5-82d1-826412f1ba1a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Brown's Super Stores, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "a88e61a8-5a2f-4dde-a4d8-0b86d6d545f6", "contactId": "f95cb6e0-ab09-4b10-8681-8c5bf457210a", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "62b70a87-79cf-492e-af8f-d9f291ba095f", "contactId": "f82ffbca-1eca-49be-9216-e8f92e629280", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Wakefern Food Corp.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "92cdd3d5-51dc-4a1b-871e-cb80863e426b", "contactId": "8080e396-9b49-4494-8385-a39c9a59f249", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "cf38daa0-73d3-4519-b735-d1df85068c15", "contactId": "da1f0ba1-d34a-45f9-ad6e-806ecdfe9ab9", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Dollar General", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "cd96d198-170f-42dd-a825-9c827c65aa7a", "contactId": "00c5d6aa-c573-4fdc-b07e-ac5967602b8c", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>'s Company", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "7d7b503a-e75c-4080-b086-815d58035aec", "contactId": "83cf39bf-d8dd-4735-a1f2-adddc5863186", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Wakefern Food Corp.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "1f840222-ca31-438c-b74e-a60ff5f9d359", "contactId": "9f82ea5b-3631-4506-b1d4-2f971bf0ba7f", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "33a14963-2f34-4673-827f-1e9552cff164", "contactId": "864a0796-515d-44b7-ac9e-0129fe162743", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Walmart", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "9336838c-ff6e-4b31-b3d3-366bdd8e796c", "contactId": "e236df63-e6ee-4236-b589-e154e4bebd62", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON>s", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "804f1454-de0e-42a5-bbbe-dd1ec8c3d7d6", "contactId": "a498cb34-dd27-41b1-adcc-b20308bb2f15", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "e58c84cf-9fd5-443a-a70a-66a90d50df13", "contactId": "4b3abd2d-70e6-431f-a8d7-3cd666e55bde", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Albertsons Companies", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:00:00.000Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "f1da898c-0506-4e8b-bcc3-3adc7934b53b", "contactId": "ffe8a079-0ba7-489b-9ccf-054c9125934e", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Whole Foods Market", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T13:26:15.333Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "f01cd2f7-84e9-46ca-8ee5-8d40fa2cd2f5", "contactId": "addd7d1f-f2ef-4b22-8a91-a809f4d4c5ca", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-09T17:37:34.203Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "6c22973e-9278-4349-926c-ce0fb3a6f121", "contactId": "1bbe6dfd-8f2e-4a30-ad4e-07ac087619ef", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T19:47:28.360Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "62c7e77d-b1e5-40b7-a4cf-d31cf6039a0d", "contactId": "b4371c4c-b720-4d3a-9693-d29bc3b0cc2b", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T19:48:24.159Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "b07d4042-6fe6-4cb9-af1d-0ea56e3e2ee7", "contactId": "90ca84f3-005a-4e81-a25e-cbd79c2e5979", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T19:49:23.866Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "d4ab0975-db4e-447a-ba91-1e1f31bca1ac", "contactId": "5a05fe8e-d191-46cd-a738-2b9f219fe252", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T19:50:21.809Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "d40c23fc-1307-4dcb-bf1c-496ad8329050", "contactId": "3b79c30a-e83c-4bfb-9f14-cf1ef3830f24", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T19:51:23.855Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "78c93dcb-0ea4-46fa-8ab2-086b1c76f950", "contactId": "496235fb-c624-4457-86e1-2981a4079ab1", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Weis Markets", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T19:52:22.272Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "059dc782-6824-4142-b1b7-8e26b7d9099c", "contactId": "43b6efed-9612-496d-b13e-b510594daa73", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-10T21:20:24.385Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "bf4cb5ad-f86c-4793-824d-34aaada13e11", "contactId": "8474035a-613a-4b81-bd0d-b0555934d79f", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Whole Foods Market", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T14:59:20.192Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "f93b8920-b161-4299-9dba-e0ab8ad037a4", "contactId": "206940fd-3406-49f1-81f0-495d181c08d1", "contactName": "<PERSON>", "contactEmail": "michael.ma<PERSON><PERSON>@gianteagle.com", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:00:19.888Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "a62a9e25-d41a-41f3-90ac-30b4d8c9d080", "contactId": "b90558dd-9055-48a7-ab0a-21314e2f9c37", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Sam's Club", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:01:12.420Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "13851c1c-ffb3-4326-8052-390e3510f142", "contactId": "0bc7e3c6-4988-40e9-9050-f25eab38a638", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "<PERSON><PERSON><PERSON>", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:02:14.474Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "cfff0228-1e1f-46fe-be98-25b3092fbeaa", "contactId": "1d79cb72-23c4-4c21-88d3-96684b1eae0b", "contactName": "<PERSON><PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:03:16.496Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.939Z"}, {"id": "4a4a9f45-0d06-4d8d-ad8e-80f9e2b01669", "contactId": "2b413848-67ac-4748-9e4e-9d7e37c2c137", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "OTR", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:04:14.489Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.939Z", "updatedAt": "2025-09-08T20:03:08.940Z"}, {"id": "6fd66b3b-82ce-4e05-b8b4-42c151c3f416", "contactId": "ca668847-f95e-4bfa-86b3-a92f87b78c07", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "Leg Up Farmers Market", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:06:15.963Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.940Z", "updatedAt": "2025-09-08T20:03:08.940Z"}, {"id": "9a8cd537-8c58-467e-ac04-be2ff5edb14b", "contactId": "058d6ea7-9a70-4d4e-8df6-e50323744978", "contactName": "<PERSON><PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "BJ's Wholesale Club", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:07:12.884Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.940Z", "updatedAt": "2025-09-08T20:03:08.940Z"}, {"id": "a411cbbc-2d3a-4327-a9d3-57a8f4e33a73", "contactId": "e0c6233b-385e-418c-94c3-83b454f88d5d", "contactName": "<PERSON>", "contactEmail": "<EMAIL>", "contactCompany": "RITE AID", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:08:13.601Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.940Z", "updatedAt": "2025-09-08T20:03:08.940Z"}, {"id": "a0c5d581-dcd3-4b65-a5ed-18e44ccbea01", "contactId": "5b44a2af-ce0e-4b51-b052-e709c8073e8d", "contactName": "Victoria Basham", "contactEmail": "<EMAIL>", "contactCompany": "Giant Eagle, Inc.", "emailType": "follow_up", "tone": "professional_friendly", "priority": "normal", "status": "queued", "scheduledFor": "2025-09-13T15:09:23.874Z", "attempts": 0, "maxAttempts": 3, "lastAttempt": null, "errorMessage": null, "sentAt": null, "createdAt": "2025-09-08T20:03:08.940Z", "updatedAt": "2025-09-08T20:03:08.940Z"}]