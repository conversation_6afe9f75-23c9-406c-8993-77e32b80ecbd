const path = require('path');
const { readJsonFile, writeJsonFile } = require('../utils/dataUtils');
const EmailQueueService = require('../services/emailQueueService');

// File paths
const EMAIL_QUEUE_FILE = path.join(__dirname, '../../data/emailQueue.json');

/**
 * Reschedule follow-up emails with new timing settings
 */
async function rescheduleFollowups() {
  try {
    console.log('⏰ Rescheduling follow-up emails with new timing...\n');

    // Load current queue
    const queue = await readJsonFile(EMAIL_QUEUE_FILE);
    
    // Initialize queue service with new settings
    const queueService = new EmailQueueService();
    await queueService.initialize();
    const settings = await queueService.getSettings();

    console.log(`📊 New queue settings:`);
    console.log(`   Emails per day: ${settings.maxEmailsPerDay}`);
    console.log(`   Interval: ${settings.minIntervalMinutes}-${settings.maxIntervalMinutes} minutes`);
    console.log(`   Hours: ${settings.startHour}:00 - ${settings.endHour}:00`);
    console.log(`   Progression enabled: ${settings.progressionEnabled}\n`);

    // Find follow-up emails that need rescheduling
    const followupEmails = queue.filter(q => q.emailType === 'follow_up' && q.status === 'queued');
    const otherEmails = queue.filter(q => !(q.emailType === 'follow_up' && q.status === 'queued'));

    console.log(`📧 Found ${followupEmails.length} follow-up emails to reschedule`);

    // Schedule emails across multiple days respecting daily limits
    const emailsPerDay = settings.maxEmailsPerDay;
    const startHour = settings.startHour;
    const endHour = settings.endHour;
    const minInterval = settings.minIntervalMinutes;
    const maxInterval = settings.maxIntervalMinutes;

    // Start scheduling from tomorrow to avoid conflicts with today's emails
    let currentDate = new Date();
    currentDate.setDate(currentDate.getDate() + 1);
    currentDate.setHours(startHour, 0, 0, 0);

    let emailsScheduledToday = 0;
    let currentScheduleTime = new Date(currentDate);

    // Reschedule each follow-up email
    for (let i = 0; i < followupEmails.length; i++) {
      const email = followupEmails[i];

      // If we've reached the daily limit, move to next day
      if (emailsScheduledToday >= emailsPerDay) {
        currentDate.setDate(currentDate.getDate() + 1);
        currentDate.setHours(startHour, 0, 0, 0);
        currentScheduleTime = new Date(currentDate);
        emailsScheduledToday = 0;
      }

      // Add random interval between emails
      const randomInterval = Math.floor(
        Math.random() * (maxInterval - minInterval) + minInterval
      );

      if (emailsScheduledToday > 0) {
        currentScheduleTime.setMinutes(currentScheduleTime.getMinutes() + randomInterval);
      }

      // If we've gone past end hour, move to next day
      if (currentScheduleTime.getHours() >= endHour) {
        currentDate.setDate(currentDate.getDate() + 1);
        currentDate.setHours(startHour, 0, 0, 0);
        currentScheduleTime = new Date(currentDate);
        emailsScheduledToday = 0;
      }

      // Update the email's schedule
      email.scheduledFor = currentScheduleTime.toISOString();
      email.updatedAt = new Date().toISOString();

      emailsScheduledToday++;

      if (i < 10) { // Show first 10 for preview
        const scheduleDate = new Date(email.scheduledFor);
        console.log(`   ${i + 1}. ${email.contactName}: ${scheduleDate.toLocaleDateString()} ${scheduleDate.toLocaleTimeString()}`);
      }
    }

    if (followupEmails.length > 10) {
      console.log(`   ... and ${followupEmails.length - 10} more`);
    }

    // Combine all emails back together
    const newQueue = [...otherEmails, ...followupEmails];

    // Save the updated queue
    await writeJsonFile(EMAIL_QUEUE_FILE, newQueue);

    console.log(`\n✅ Rescheduling complete!`);
    console.log(`   Total emails rescheduled: ${followupEmails.length}`);
    console.log(`   New queue size: ${newQueue.length}`);

    // Show timing distribution
    const emailsByDay = {};
    followupEmails.forEach(email => {
      const day = new Date(email.scheduledFor).toDateString();
      emailsByDay[day] = (emailsByDay[day] || 0) + 1;
    });

    console.log(`\n📅 Distribution across days:`);
    Object.entries(emailsByDay).forEach(([day, count]) => {
      console.log(`   ${day}: ${count} emails`);
    });

  } catch (error) {
    console.error('❌ Error during rescheduling:', error.message);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  rescheduleFollowups()
    .then(() => {
      console.log('\n🎉 Rescheduling completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Rescheduling failed:', error.message);
      process.exit(1);
    });
}

module.exports = { rescheduleFollowups };
